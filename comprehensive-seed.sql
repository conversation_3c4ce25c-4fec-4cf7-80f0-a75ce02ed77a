-- =====================================================
-- COMPREHENSIVE SEED DATA FOR AI NAVIGATOR
-- Run this in Supabase SQL Editor to populate all reference data
-- =====================================================

-- Step 1: Insert Entity Types
INSERT INTO "public"."entity_types" ("id", "name", "description", "slug", "icon_url", "created_at", "updated_at") 
VALUES 
    (gen_random_uuid(), 'AI Tool', 'Artificial Intelligence tools and platforms', 'ai-tool', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'API', 'Application Programming Interfaces and web services', 'api', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Dataset', 'Data collections and datasets for training and research', 'dataset', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Model', 'Pre-trained machine learning models', 'model', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Platform', 'Development and deployment platforms', 'platform', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Library', 'Code libraries and frameworks', 'library', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Service', 'Cloud services and SaaS platforms', 'service', NULL, NOW(), NOW())
ON CONFLICT ("name") DO NOTHING;

-- Step 2: Insert Categories (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        INSERT INTO "public"."categories" ("id", "name", "description", "slug", "created_at", "updated_at") 
        VALUES 
            (gen_random_uuid(), 'Machine Learning', 'Tools and platforms for machine learning', 'machine-learning', NOW(), NOW()),
            (gen_random_uuid(), 'Natural Language Processing', 'NLP tools and language models', 'natural-language-processing', NOW(), NOW()),
            (gen_random_uuid(), 'Computer Vision', 'Image and video processing tools', 'computer-vision', NOW(), NOW()),
            (gen_random_uuid(), 'Data Science', 'Data analysis and visualization tools', 'data-science', NOW(), NOW()),
            (gen_random_uuid(), 'Development Tools', 'Developer tools and IDEs', 'development-tools', NOW(), NOW()),
            (gen_random_uuid(), 'Automation', 'Workflow automation and productivity tools', 'automation', NOW(), NOW()),
            (gen_random_uuid(), 'Analytics', 'Business intelligence and analytics platforms', 'analytics', NOW(), NOW()),
            (gen_random_uuid(), 'Content Creation', 'Tools for creating and editing content', 'content-creation', NOW(), NOW()),
            (gen_random_uuid(), 'Research', 'Academic and research tools', 'research', NOW(), NOW()),
            (gen_random_uuid(), 'Business Intelligence', 'BI and enterprise analytics tools', 'business-intelligence', NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING;
    END IF;
END $$;

-- Step 3: Insert Tags (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        INSERT INTO "public"."tags" ("id", "name", "description", "slug", "created_at", "updated_at") 
        VALUES 
            (gen_random_uuid(), 'Free', 'Free to use tools and services', 'free', NOW(), NOW()),
            (gen_random_uuid(), 'Open Source', 'Open source projects and tools', 'open-source', NOW(), NOW()),
            (gen_random_uuid(), 'API Available', 'Tools that provide APIs', 'api-available', NOW(), NOW()),
            (gen_random_uuid(), 'Cloud-based', 'Cloud-based services', 'cloud-based', NOW(), NOW()),
            (gen_random_uuid(), 'Enterprise', 'Enterprise-grade solutions', 'enterprise', NOW(), NOW()),
            (gen_random_uuid(), 'Beginner Friendly', 'Easy to use for beginners', 'beginner-friendly', NOW(), NOW()),
            (gen_random_uuid(), 'Advanced', 'Advanced tools for experts', 'advanced', NOW(), NOW()),
            (gen_random_uuid(), 'No-Code', 'No coding required', 'no-code', NOW(), NOW()),
            (gen_random_uuid(), 'Real-time', 'Real-time processing capabilities', 'real-time', NOW(), NOW()),
            (gen_random_uuid(), 'Mobile App', 'Has mobile application', 'mobile-app', NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING;
    END IF;
END $$;

-- Step 4: Insert Features (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'features') THEN
        INSERT INTO "public"."features" ("id", "name", "description", "slug", "created_at", "updated_at") 
        VALUES 
            (gen_random_uuid(), 'Text Generation', 'Generate human-like text content', 'text-generation', NOW(), NOW()),
            (gen_random_uuid(), 'Image Generation', 'Create images from text descriptions', 'image-generation', NOW(), NOW()),
            (gen_random_uuid(), 'Code Generation', 'Generate code in various programming languages', 'code-generation', NOW(), NOW()),
            (gen_random_uuid(), 'Data Analysis', 'Analyze and visualize data', 'data-analysis', NOW(), NOW()),
            (gen_random_uuid(), 'Translation', 'Translate text between languages', 'translation', NOW(), NOW()),
            (gen_random_uuid(), 'Summarization', 'Summarize long text content', 'summarization', NOW(), NOW()),
            (gen_random_uuid(), 'Question Answering', 'Answer questions based on context', 'question-answering', NOW(), NOW()),
            (gen_random_uuid(), 'Speech Recognition', 'Convert speech to text', 'speech-recognition', NOW(), NOW()),
            (gen_random_uuid(), 'Text-to-Speech', 'Convert text to speech', 'text-to-speech', NOW(), NOW()),
            (gen_random_uuid(), 'Sentiment Analysis', 'Analyze sentiment in text', 'sentiment-analysis', NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING;
    END IF;
END $$;

-- Step 5: Insert App Settings
INSERT INTO "public"."app_settings" ("key", "value", "description", "created_at", "updated_at") 
VALUES 
    ('default_llm_provider', 'openai', 'Default LLM provider for the application', NOW(), NOW()),
    ('max_entities_per_page', '20', 'Maximum number of entities to show per page', NOW(), NOW()),
    ('enable_user_submissions', 'true', 'Allow users to submit new entities', NOW(), NOW()),
    ('enable_tool_requests', 'true', 'Allow users to request new tools', NOW(), NOW()),
    ('maintenance_mode', 'false', 'Enable maintenance mode', NOW(), NOW()),
    ('featured_entities_count', '6', 'Number of featured entities to show on homepage', NOW(), NOW()),
    ('enable_reviews', 'true', 'Allow users to write reviews', NOW(), NOW()),
    ('min_review_length', '50', 'Minimum review length in characters', NOW(), NOW())
ON CONFLICT ("key") DO NOTHING;

-- Success message
SELECT 'Reference data seeded successfully!' as result;
SELECT 'Next: Run the sample entities script' as next_step;
