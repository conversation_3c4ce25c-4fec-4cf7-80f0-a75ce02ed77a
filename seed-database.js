const { PrismaClient } = require('./generated/prisma');

async function seedDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🌱 Starting database seeding...');
    
    // Test connection
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Seed entity types
    console.log('📝 Seeding entity types...');
    const entityTypes = await Promise.all([
      prisma.entityType.upsert({
        where: { name: 'AI Tool' },
        update: {},
        create: {
          name: 'AI Tool',
          description: 'Artificial Intelligence tools and platforms',
          slug: 'ai-tool',
        },
      }),
      prisma.entityType.upsert({
        where: { name: 'API' },
        update: {},
        create: {
          name: 'API',
          description: 'Application Programming Interfaces and web services',
          slug: 'api',
        },
      }),
      prisma.entityType.upsert({
        where: { name: 'Dataset' },
        update: {},
        create: {
          name: 'Dataset',
          description: 'Data collections and datasets for training and research',
          slug: 'dataset',
        },
      }),
      prisma.entityType.upsert({
        where: { name: 'Model' },
        update: {},
        create: {
          name: 'Model',
          description: 'Pre-trained machine learning models',
          slug: 'model',
        },
      }),
      prisma.entityType.upsert({
        where: { name: 'Platform' },
        update: {},
        create: {
          name: 'Platform',
          description: 'Development and deployment platforms',
          slug: 'platform',
        },
      }),
      prisma.entityType.upsert({
        where: { name: 'Library' },
        update: {},
        create: {
          name: 'Library',
          description: 'Code libraries and frameworks',
          slug: 'library',
        },
      }),
      prisma.entityType.upsert({
        where: { name: 'Service' },
        update: {},
        create: {
          name: 'Service',
          description: 'Cloud services and SaaS platforms',
          slug: 'service',
        },
      }),
    ]);
    
    console.log(`✅ Created ${entityTypes.length} entity types`);
    
    // Seed categories if the table exists
    try {
      console.log('📝 Seeding categories...');
      const categories = await Promise.all([
        prisma.category.upsert({
          where: { name: 'Machine Learning' },
          update: {},
          create: {
            name: 'Machine Learning',
            description: 'Tools and platforms for machine learning',
            slug: 'machine-learning',
          },
        }),
        prisma.category.upsert({
          where: { name: 'Natural Language Processing' },
          update: {},
          create: {
            name: 'Natural Language Processing',
            description: 'NLP tools and language models',
            slug: 'natural-language-processing',
          },
        }),
        prisma.category.upsert({
          where: { name: 'Computer Vision' },
          update: {},
          create: {
            name: 'Computer Vision',
            description: 'Image and video processing tools',
            slug: 'computer-vision',
          },
        }),
        prisma.category.upsert({
          where: { name: 'Data Science' },
          update: {},
          create: {
            name: 'Data Science',
            description: 'Data analysis and visualization tools',
            slug: 'data-science',
          },
        }),
      ]);
      console.log(`✅ Created ${categories.length} categories`);
    } catch (error) {
      console.log('ℹ️ Categories table not found, skipping...');
    }
    
    // Seed app settings
    console.log('📝 Seeding app settings...');
    const appSettings = await Promise.all([
      prisma.appSettings.upsert({
        where: { key: 'default_llm_provider' },
        update: {},
        create: {
          key: 'default_llm_provider',
          value: 'openai',
          description: 'Default LLM provider for the application',
        },
      }),
      prisma.appSettings.upsert({
        where: { key: 'max_entities_per_page' },
        update: {},
        create: {
          key: 'max_entities_per_page',
          value: '20',
          description: 'Maximum number of entities to show per page',
        },
      }),
      prisma.appSettings.upsert({
        where: { key: 'enable_user_submissions' },
        update: {},
        create: {
          key: 'enable_user_submissions',
          value: 'true',
          description: 'Allow users to submit new entities',
        },
      }),
      prisma.appSettings.upsert({
        where: { key: 'enable_tool_requests' },
        update: {},
        create: {
          key: 'enable_tool_requests',
          value: 'true',
          description: 'Allow users to request new tools',
        },
      }),
    ]);
    
    console.log(`✅ Created ${appSettings.length} app settings`);
    
    // Show summary
    const counts = await Promise.all([
      prisma.entityType.count(),
      prisma.appSettings.count(),
      prisma.user.count(),
    ]);
    
    console.log('\n📊 Database Summary:');
    console.log(`   Entity Types: ${counts[0]}`);
    console.log(`   App Settings: ${counts[1]}`);
    console.log(`   Users: ${counts[2]}`);
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('🚀 Your application is ready to use!');
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
    
    if (error.code === 'P1001') {
      console.log('\n🔧 Connection issue detected. Please check:');
      console.log('1. Your internet connection');
      console.log('2. Supabase project is running');
      console.log('3. Database credentials in .env are correct');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedDatabase()
  .then(() => {
    console.log('✨ Seeding process completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Seeding process failed:', error);
    process.exit(1);
  });
