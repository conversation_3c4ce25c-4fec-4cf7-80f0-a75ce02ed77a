import { jest } from '@jest/globals';

export const mockPrismaService = {
  // Connection methods
  $connect: jest.fn().mockImplementation(() => Promise.resolve()),
  $disconnect: jest.fn().mockImplementation(() => Promise.resolve()),
  $transaction: jest.fn(),

  // Feature model methods
  feature: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },

  // Entity model methods
  entity: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },

  // User model methods
  user: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    upsert: jest.fn(),
  },

  // Review model methods
  review: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },

  // Category model methods
  category: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },

  // Tag model methods
  tag: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },

  // EntityCategory junction table
  entityCategory: {
    create: jest.fn(),
    findMany: jest.fn(),
    delete: jest.fn(),
  },

  // EntityTag junction table
  entityTag: {
    create: jest.fn(),
    findMany: jest.fn(),
    delete: jest.fn(),
  },

  // UserSavedEntity junction table
  userSavedEntity: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
  },
};

// Helper function to reset all mocks
export const resetPrismaMocks = () => {
  Object.values(mockPrismaService).forEach((model) => {
    if (typeof model === 'object' && model !== null) {
      Object.values(model).forEach((method) => {
        if (jest.isMockFunction(method)) {
          method.mockReset();
        }
      });
    } else if (jest.isMockFunction(model)) {
      model.mockReset();
    }
  });
};

// Mock Prisma errors for testing
export const mockPrismaErrors = {
  P2025: new (class extends Error {
    code = 'P2025';
    meta = { cause: 'Record not found' };
    constructor() {
      super('Record not found');
      this.name = 'PrismaClientKnownRequestError';
    }
  })(),
  
  P2002: new (class extends Error {
    code = 'P2002';
    meta = { target: ['name'] };
    constructor() {
      super('Unique constraint failed');
      this.name = 'PrismaClientKnownRequestError';
    }
  })(),
  
  ValidationError: new (class extends Error {
    constructor() {
      super('Validation error');
      this.name = 'PrismaClientValidationError';
    }
  })(),
};
