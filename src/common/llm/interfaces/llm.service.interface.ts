export interface LlmRecommendation {
  recommendedEntityIds: string[];
  explanation: string;
}

// Chat-specific interfaces
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface UserIntent {
  type: 'discovery' | 'comparison' | 'specific_tool' | 'general_question' | 'refinement';
  confidence: number;
  entities?: string[]; // Entity IDs or names mentioned
  categories?: string[]; // Categories of interest
  features?: string[]; // Specific features mentioned
  constraints?: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    use_case?: string;
  };
}

export interface ConversationContext {
  sessionId: string;
  userId: string;
  messages: ChatMessage[];
  currentIntent?: UserIntent;
  discoveredEntities: string[]; // Entity IDs discovered so far
  userPreferences: {
    budget?: 'free' | 'low' | 'medium' | 'high';
    technical_level?: 'beginner' | 'intermediate' | 'advanced';
    preferred_categories?: string[];
    excluded_categories?: string[];
  };
  conversationStage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
  metadata: {
    startedAt: Date;
    lastActiveAt: Date;
    totalMessages: number;
    entitiesShown: string[];
  };
}

export interface ChatResponse {
  message: string;
  intent: UserIntent;
  suggestedActions?: Array<{
    type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';
    label: string;
    data?: any;
  }>;
  discoveredEntities?: Array<{
    id: string;
    name: string;
    relevanceScore: number;
    reason: string;
  }>;
  followUpQuestions?: string[];
  shouldTransitionToRecommendations: boolean;
  conversationStage: ConversationContext['conversationStage'];
  metadata: {
    responseTime: number;
    llmProvider: string;
    tokensUsed?: number;
  };
}

export interface CandidateEntity {
  id: string;
  name: string;
  shortDescription: string | null;
  description: string | null;
  entityType: {
    name: string;
    slug: string;
  };
  categories: Array<{
    category: {
      name: string;
      slug: string;
    };
  }>;
  tags: Array<{
    tag: {
      name: string;
      slug: string;
    };
  }>;
  features: Array<{
    feature: {
      name: string;
      slug: string;
    };
  }>;
  // Additional details that might be useful for LLM context
  websiteUrl?: string | null;
  logoUrl?: string | null;
  avgRating?: number;
  reviewCount?: number;
}

export interface ILlmService {
  /**
   * Get AI-powered recommendations based on a problem description and candidate entities
   * @param problemDescription - The user's problem description
   * @param candidateEntities - Array of entities found through vector search
   * @returns Promise containing recommended entity IDs and explanation
   */
  getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation>;

  /**
   * Generate a conversational chat response based on user message and conversation context
   * @param userMessage - The user's current message
   * @param context - Full conversation context including history and discovered entities
   * @param candidateEntities - Optional array of entities to consider in the response
   * @returns Promise containing chat response with intent analysis and suggestions
   */
  getChatResponse(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse>;

  /**
   * Classify user intent from their message and conversation context
   * @param userMessage - The user's current message
   * @param context - Conversation context for better intent understanding
   * @returns Promise containing classified intent with confidence score
   */
  classifyIntent(
    userMessage: string,
    context: ConversationContext,
  ): Promise<UserIntent>;

  /**
   * Generate intelligent follow-up questions to guide the conversation
   * @param context - Current conversation context
   * @returns Promise containing array of suggested follow-up questions
   */
  generateFollowUpQuestions(
    context: ConversationContext,
  ): Promise<string[]>;

  /**
   * Determine if the conversation should transition to the recommendations endpoint
   * @param context - Current conversation context
   * @returns Promise containing boolean decision and reasoning
   */
  shouldTransitionToRecommendations(
    context: ConversationContext,
  ): Promise<{ shouldTransition: boolean; reason: string }>;
}
