import { Modu<PERSON> } from '@nestjs/common';
import { BookmarksService } from './bookmarks.service';
import { BookmarksController } from './bookmarks.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module'; // For JwtAuthGuard dependency
import { LoggerModule } from '../common/logger/logger.module';
// import { EntitiesModule } from '../entities/entities.module'; // If EntitiesService is needed

@Module({
  imports: [
    PrismaModule,
    AuthModule, // To make JwtAuthGuard available and req.user populated
    LoggerModule, // For structured logging
    // EntitiesModule, // If direct interaction with EntitiesService for validation needed
  ],
  controllers: [BookmarksController],
  providers: [BookmarksService],
})
export class BookmarksModule {} 