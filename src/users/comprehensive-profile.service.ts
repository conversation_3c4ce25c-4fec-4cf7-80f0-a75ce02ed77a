import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UserPreferencesService } from './user-preferences.service';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ComprehensiveProfileResponseDto, UserStatsDto, ProfileActivityDto, UserProfileDto } from './dto/comprehensive-profile.dto';

@Injectable()
export class ComprehensiveProfileService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly userPreferencesService: UserPreferencesService,
    private readonly activityLoggerService: ActivityLoggerService,
  ) {}

  async getComprehensiveProfile(userId: string): Promise<ComprehensiveProfileResponseDto> {
    // Fetch user data with all the statistics
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get user preferences (creates default if not exists)
    const preferences = await this.userPreferencesService.getUserPreferences(userId);

    // Get recent activities
    const recentActivities = await this.activityLoggerService.getRecentActivities(userId, 10);

    // Map user data
    const userProfile: UserProfileDto = {
      id: user.id,
      authUserId: user.authUserId,
      username: user.username || undefined,
      displayName: user.displayName || undefined,
      email: user.email,
      role: user.role,
      status: user.status,
      technicalLevel: user.technicalLevel || undefined,
      profilePictureUrl: user.profilePictureUrl || undefined,
      bio: user.bio || undefined,
      socialLinks: user.socialLinks,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin || undefined,
    };

    // Map statistics (using denormalized fields)
    const stats: UserStatsDto = {
      bookmarksCount: user.bookmarksCount,
      reviewsCount: user.reviewsCount,
      toolsSubmitted: user.toolsSubmitted,
      toolsApproved: user.toolsApproved,
      requestsMade: user.requestsMade,
      requestsFulfilled: user.requestsFulfilled,
      reputationScore: user.reputationScore,
      memberSince: user.createdAt,
    };

    // Map recent activities
    const recentActivity: ProfileActivityDto[] = recentActivities.map(activity => ({
      id: activity.id,
      type: activity.type,
      description: activity.description,
      entityId: activity.entityId,
      entityName: activity.entityName,
      entitySlug: activity.entitySlug,
      createdAt: activity.createdAt,
    }));

    return {
      user: userProfile,
      preferences,
      stats,
      recentActivity,
    };
  }

  // Delegate activity logging to the dedicated service
  async logActivity(
    userId: string,
    type: 'BOOKMARK' | 'REVIEW' | 'SUBMISSION' | 'REQUEST' | 'VOTE',
    description: string,
    entityId?: string,
    entityName?: string,
    entitySlug?: string,
  ): Promise<void> {
    await this.activityLoggerService.logActivity({
      userId,
      type,
      description,
      entityId,
      entityName,
      entitySlug,
    });
  }
}
