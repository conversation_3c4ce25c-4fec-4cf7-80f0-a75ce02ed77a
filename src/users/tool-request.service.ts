import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ValidationService } from '../common/validation.service';
import { 
  CreateToolRequestDto, 
  ToolRequestResponseDto, 
  ListToolRequestsDto, 
  PaginatedToolRequestsResponseDto 
} from './dto/tool-request.dto';
import { ToolRequest } from '../../generated/prisma';

@Injectable()
export class ToolRequestService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly activityLogger: ActivityLoggerService,
    private readonly validationService: ValidationService,
  ) {}

  async createToolRequest(
    userId: string,
    createDto: CreateToolRequestDto,
  ): Promise<ToolRequestResponseDto> {
    // Validate the tool request
    await this.validationService.validateToolRequest(userId, createDto.toolName);

    const toolRequest = await this.prismaService.toolRequest.create({
      data: {
        userId,
        toolName: createDto.toolName,
        description: createDto.description,
        reason: createDto.reason,
        categorySuggestion: createDto.categorySuggestion,
        websiteUrl: createDto.websiteUrl,
        priority: createDto.priority || 'MEDIUM',
      },
    });

    // Log the tool request activity
    await this.activityLogger.logRequestActivity(
      userId,
      createDto.toolName,
      'created'
    );

    return this.mapToResponseDto(toolRequest);
  }

  async getUserToolRequests(
    userId: string,
    listDto: ListToolRequestsDto,
  ): Promise<PaginatedToolRequestsResponseDto> {
    const { page = 1, limit = 20, status, priority } = listDto;
    const skip = (page - 1) * limit;

    const where: any = { userId };
    if (status) where.status = status;
    if (priority) where.priority = priority;

    const [toolRequests, total] = await Promise.all([
      this.prismaService.toolRequest.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prismaService.toolRequest.count({ where }),
    ]);

    return {
      data: toolRequests.map(this.mapToResponseDto),
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getAllToolRequests(
    listDto: ListToolRequestsDto,
  ): Promise<PaginatedToolRequestsResponseDto> {
    const { page = 1, limit = 20, status, priority } = listDto;
    const skip = (page - 1) * limit;

    const where: any = {};
    if (status) where.status = status;
    if (priority) where.priority = priority;

    const [toolRequests, total] = await Promise.all([
      this.prismaService.toolRequest.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              displayName: true,
            },
          },
        },
      }),
      this.prismaService.toolRequest.count({ where }),
    ]);

    return {
      data: toolRequests.map(this.mapToResponseDto),
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  private mapToResponseDto(toolRequest: ToolRequest): ToolRequestResponseDto {
    return {
      id: toolRequest.id,
      userId: toolRequest.userId,
      toolName: toolRequest.toolName,
      description: toolRequest.description,
      reason: toolRequest.reason,
      categorySuggestion: toolRequest.categorySuggestion || undefined,
      websiteUrl: toolRequest.websiteUrl || undefined,
      priority: toolRequest.priority,
      status: toolRequest.status,
      adminNotes: toolRequest.adminNotes || undefined,
      votes: toolRequest.votes,
      createdAt: toolRequest.createdAt,
      updatedAt: toolRequest.updatedAt,
    };
  }
}
