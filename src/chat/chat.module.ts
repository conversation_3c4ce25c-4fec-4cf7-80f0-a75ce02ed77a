import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MemoryConversationStateService } from './services/memory-conversation-state.service';
import { ConversationManagerService } from './services/conversation-manager.service';
import { ChatService } from './services/chat.service';
import { ChatErrorHandlerService } from './services/chat-error-handler.service';
import { LlmFailoverService } from './services/llm-failover.service';
import { ChatPerformanceMonitorService } from './services/chat-performance-monitor.service';
import { ChatCacheService } from './services/chat-cache.service';
import { ChatController } from './chat.controller';
import { EntitiesModule } from '../entities/entities.module';
import { LlmModule } from '../common/llm/llm.module';

@Module({
  imports: [
    ConfigModule,
    EntitiesModule,
    LlmModule,
  ],
  controllers: [ChatController],
  providers: [
    {
      provide: 'IConversationStateService',
      useClass: MemoryConversationStateService,
    },
    ConversationManagerService,
    ChatErrorHandlerService,
    LlmFailoverService,
    ChatPerformanceMonitorService,
    ChatCacheService,
    ChatService,
  ],
  exports: [
    'IConversationStateService',
    ConversationManagerService,
    ChatService,
  ],
})
export class ChatModule {}
