import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { Chat<PERSON>ontroller } from './chat.controller';
import { ChatService } from './services/chat.service';
import { SendChatMessageDto } from './dto/send-chat-message.dto';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ConversationHistoryResponseDto } from './dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from './dto/get-conversation-history.dto';

describe('ChatController', () => {
  let controller: ChatController;
  let chatService: jest.Mocked<ChatService>;

  const mockReqUser = {
    authData: {
      sub: 'user-123',
      email: '<EMAIL>',
    },
    dbProfile: null,
    profileExistsInDb: false,
  };

  const mockChatResponse: ChatResponseDto = {
    message: 'Hello! How can I help you find AI tools today?',
    session_id: 'chat_test-session',
    conversation_stage: 'greeting',
    should_transition_to_recommendations: false,
    follow_up_questions: ['What type of AI tools are you looking for?'],
    metadata: {
      response_time: 1000,
      llm_provider: 'OPENAI',
    },
    generated_at: new Date(),
  };

  const mockConversationHistory: ConversationHistoryResponseDto = {
    session_id: 'chat_test-session',
    conversation_stage: 'discovery',
    messages: [
      {
        id: 'msg-1',
        role: 'user',
        content: 'Hello',
        timestamp: new Date(),
      },
      {
        id: 'msg-2',
        role: 'assistant',
        content: 'Hi there!',
        timestamp: new Date(),
      },
    ],
    total_messages: 2,
    discovered_entities_count: 0,
    started_at: new Date(),
    last_active_at: new Date(),
  };

  beforeEach(async () => {
    const mockChatService = {
      sendMessage: jest.fn(),
      getConversationHistory: jest.fn(),
      endConversation: jest.fn(),
      getUserActiveSessions: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChatController],
      providers: [
        {
          provide: ChatService,
          useValue: mockChatService,
        },
      ],
    }).compile();

    controller = module.get<ChatController>(ChatController);
    chatService = module.get(ChatService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('sendMessage', () => {
    it('should send a chat message successfully', async () => {
      const sendMessageDto: SendChatMessageDto = {
        message: 'I need help finding AI tools',
      };

      chatService.sendMessage.mockResolvedValue(mockChatResponse);

      const result = await controller.sendMessage(sendMessageDto, mockReqUser);

      expect(result).toEqual(mockChatResponse);
      expect(chatService.sendMessage).toHaveBeenCalledWith('user-123', sendMessageDto);
    });

    it('should throw BadRequestException for empty message', async () => {
      const sendMessageDto: SendChatMessageDto = {
        message: '',
      };

      await expect(controller.sendMessage(sendMessageDto, mockReqUser))
        .rejects.toThrow(BadRequestException);

      expect(chatService.sendMessage).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for whitespace-only message', async () => {
      const sendMessageDto: SendChatMessageDto = {
        message: '   ',
      };

      await expect(controller.sendMessage(sendMessageDto, mockReqUser))
        .rejects.toThrow(BadRequestException);

      expect(chatService.sendMessage).not.toHaveBeenCalled();
    });

    it('should handle service errors gracefully', async () => {
      const sendMessageDto: SendChatMessageDto = {
        message: 'Test message',
      };

      chatService.sendMessage.mockRejectedValue(new Error('Service error'));

      await expect(controller.sendMessage(sendMessageDto, mockReqUser))
        .rejects.toThrow('Unable to process chat message at this time');
    });
  });

  describe('getConversationHistory', () => {
    it('should get conversation history successfully', async () => {
      const getHistoryDto: GetConversationHistoryDto = { limit: 20 };
      const sessionId = 'chat_test-session';

      chatService.getConversationHistory.mockResolvedValue(mockConversationHistory);

      const result = await controller.getConversationHistory(
        sessionId,
        getHistoryDto,
        mockReqUser,
      );

      expect(result).toEqual(mockConversationHistory);
      expect(chatService.getConversationHistory).toHaveBeenCalledWith(
        'user-123',
        sessionId,
        getHistoryDto,
      );
    });

    it('should throw BadRequestException for empty session ID', async () => {
      const getHistoryDto: GetConversationHistoryDto = { limit: 20 };

      await expect(controller.getConversationHistory('', getHistoryDto, mockReqUser))
        .rejects.toThrow(BadRequestException);

      expect(chatService.getConversationHistory).not.toHaveBeenCalled();
    });

    it('should throw ForbiddenException for unauthorized access', async () => {
      const getHistoryDto: GetConversationHistoryDto = { limit: 20 };
      const sessionId = 'chat_test-session';

      chatService.getConversationHistory.mockRejectedValue(
        new Error('Session does not belong to the user'),
      );

      await expect(controller.getConversationHistory(sessionId, getHistoryDto, mockReqUser))
        .rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException for non-existent session', async () => {
      const getHistoryDto: GetConversationHistoryDto = { limit: 20 };
      const sessionId = 'chat_non-existent';

      chatService.getConversationHistory.mockRejectedValue(
        new Error('Conversation not found'),
      );

      await expect(controller.getConversationHistory(sessionId, getHistoryDto, mockReqUser))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('endConversation', () => {
    it('should end conversation successfully', async () => {
      const sessionId = 'chat_test-session';

      chatService.endConversation.mockResolvedValue(undefined);

      await controller.endConversation(sessionId, mockReqUser);

      expect(chatService.endConversation).toHaveBeenCalledWith('user-123', sessionId);
    });
  });

  describe('getUserActiveSessions', () => {
    it('should get user active sessions successfully', async () => {
      const mockSessions = ['chat_session-1', 'chat_session-2'];

      chatService.getUserActiveSessions.mockResolvedValue(mockSessions);

      const result = await controller.getUserActiveSessions(mockReqUser);

      expect(result).toEqual({
        sessions: mockSessions,
        total: 2,
      });
      expect(chatService.getUserActiveSessions).toHaveBeenCalledWith('user-123');
    });

    it('should return empty array when no active sessions', async () => {
      chatService.getUserActiveSessions.mockResolvedValue([]);

      const result = await controller.getUserActiveSessions(mockReqUser);

      expect(result).toEqual({
        sessions: [],
        total: 0,
      });
    });
  });
});
