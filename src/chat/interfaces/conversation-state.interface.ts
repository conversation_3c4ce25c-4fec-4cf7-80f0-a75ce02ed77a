import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Interface for conversation state storage
 * Designed to be implementation-agnostic (in-memory, Redis, database, etc.)
 */
export interface IConversationStateService {
  /**
   * Store or update conversation context
   * @param sessionId - Unique session identifier
   * @param context - Conversation context to store
   * @param ttlSeconds - Time to live in seconds (optional)
   */
  setConversationContext(
    sessionId: string,
    context: ConversationContext,
    ttlSeconds?: number,
  ): Promise<void>;

  /**
   * Retrieve conversation context by session ID
   * @param sessionId - Unique session identifier
   * @returns Conversation context or null if not found
   */
  getConversationContext(sessionId: string): Promise<ConversationContext | null>;

  /**
   * Delete conversation context
   * @param sessionId - Unique session identifier
   */
  deleteConversationContext(sessionId: string): Promise<void>;

  /**
   * Check if conversation context exists
   * @param sessionId - Unique session identifier
   */
  hasConversationContext(sessionId: string): Promise<boolean>;

  /**
   * Get all active session IDs for a user
   * @param userId - User identifier
   */
  getUserActiveSessions(userId: string): Promise<string[]>;

  /**
   * Clean up expired conversations
   */
  cleanupExpiredConversations(): Promise<number>;

  /**
   * Get conversation statistics
   */
  getStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    memoryUsage?: number;
  }>;
}

/**
 * Configuration for conversation state service
 */
export interface ConversationStateConfig {
  defaultTtlSeconds: number;
  maxSessions: number;
  cleanupIntervalMs: number;
  enableMetrics: boolean;
}

/**
 * Conversation session metadata
 */
export interface ConversationSession {
  sessionId: string;
  userId: string;
  context: ConversationContext;
  createdAt: Date;
  lastAccessedAt: Date;
  expiresAt: Date;
}
