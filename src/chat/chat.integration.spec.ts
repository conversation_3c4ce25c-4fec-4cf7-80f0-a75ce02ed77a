import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { ChatModule } from './chat.module';
import { EntitiesModule } from '../entities/entities.module';
import { LlmModule } from '../common/llm/llm.module';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { ThrottlerModule } from '@nestjs/throttler';

describe('Chat Integration Tests', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        ThrottlerModule.forRoot([{
          ttl: 60000,
          limit: 100, // Higher limit for tests
        }]),
        PrismaModule,
        AuthModule,
        EntitiesModule,
        LlmModule,
        ChatModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token for testing (you'll need to implement this based on your auth setup)
    authToken = await getTestAuthToken();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /chat', () => {
    it('should start a new conversation', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'Hello, I need help finding AI tools for code documentation',
        })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('session_id');
      expect(response.body).toHaveProperty('conversation_stage');
      expect(response.body).toHaveProperty('metadata');
      expect(response.body.conversation_stage).toBe('greeting');
      expect(response.body.session_id).toMatch(/^chat_/);
    });

    it('should continue an existing conversation', async () => {
      // First message to create session
      const firstResponse = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'I need AI tools for my development work',
        })
        .expect(200);

      const sessionId = firstResponse.body.session_id;

      // Second message in same session
      const secondResponse = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'I work with Python and JavaScript',
          session_id: sessionId,
        })
        .expect(200);

      expect(secondResponse.body.session_id).toBe(sessionId);
      expect(secondResponse.body.conversation_stage).toMatch(/discovery|refinement/);
    });

    it('should handle user preferences', async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'I need budget-friendly AI tools',
          user_preferences: {
            budget: 'free',
            technical_level: 'beginner',
            preferred_categories: ['Developer Tools'],
          },
        })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toBeDefined();
    });

    it('should reject empty messages', async () => {
      await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: '',
        })
        .expect(400);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .post('/chat')
        .send({
          message: 'Test message',
        })
        .expect(401);
    });

    it('should handle rate limiting', async () => {
      // This test would need to be adjusted based on your actual rate limits
      const promises = [];
      for (let i = 0; i < 25; i++) {
        promises.push(
          request(app.getHttpServer())
            .post('/chat')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              message: `Test message ${i}`,
            })
        );
      }

      const responses = await Promise.allSettled(promises);
      const rateLimitedResponses = responses.filter(
        (response) => response.status === 'fulfilled' && response.value.status === 429
      );

      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('GET /chat/:sessionId/history', () => {
    let sessionId: string;

    beforeEach(async () => {
      // Create a session with some messages
      const response = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'Start conversation',
        });
      
      sessionId = response.body.session_id;

      // Add another message
      await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'Continue conversation',
          session_id: sessionId,
        });
    });

    it('should get conversation history', async () => {
      const response = await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('session_id', sessionId);
      expect(response.body).toHaveProperty('messages');
      expect(response.body.messages).toBeInstanceOf(Array);
      expect(response.body.messages.length).toBeGreaterThan(0);
      expect(response.body).toHaveProperty('total_messages');
      expect(response.body).toHaveProperty('conversation_stage');
    });

    it('should limit messages with query parameter', async () => {
      const response = await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history?limit=1`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.messages.length).toBeLessThanOrEqual(1);
    });

    it('should reject invalid session ID', async () => {
      await request(app.getHttpServer())
        .get('/chat/invalid-session/history')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history`)
        .expect(401);
    });
  });

  describe('DELETE /chat/:sessionId', () => {
    let sessionId: string;

    beforeEach(async () => {
      const response = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'Test session to delete',
        });
      
      sessionId = response.body.session_id;
    });

    it('should end conversation successfully', async () => {
      await request(app.getHttpServer())
        .delete(`/chat/${sessionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(204);

      // Verify session is deleted by trying to get history
      await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .delete(`/chat/${sessionId}`)
        .expect(401);
    });
  });

  describe('GET /chat/sessions', () => {
    it('should get user active sessions', async () => {
      // Create a few sessions
      const session1 = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ message: 'Session 1' });

      const session2 = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ message: 'Session 2' });

      const response = await request(app.getHttpServer())
        .get('/chat/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('sessions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.sessions).toBeInstanceOf(Array);
      expect(response.body.total).toBe(response.body.sessions.length);
      expect(response.body.sessions).toContain(session1.body.session_id);
      expect(response.body.sessions).toContain(session2.body.session_id);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get('/chat/sessions')
        .expect(401);
    });
  });

  describe('Conversation Flow Integration', () => {
    it('should handle a complete conversation flow', async () => {
      // Start conversation
      const greeting = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'Hi, I need help finding AI tools',
        })
        .expect(200);

      const sessionId = greeting.body.session_id;
      expect(greeting.body.conversation_stage).toBe('greeting');

      // Continue with more specific request
      const discovery = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'I need tools for automated code documentation in Python',
          session_id: sessionId,
        })
        .expect(200);

      expect(discovery.body.session_id).toBe(sessionId);
      expect(discovery.body.conversation_stage).toMatch(/discovery|refinement/);

      // Refine requirements
      const refinement = await request(app.getHttpServer())
        .post('/chat')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          message: 'I prefer free tools that integrate with VS Code',
          session_id: sessionId,
          user_preferences: {
            budget: 'free',
            technical_level: 'intermediate',
          },
        })
        .expect(200);

      expect(refinement.body.session_id).toBe(sessionId);

      // Get conversation history
      const history = await request(app.getHttpServer())
        .get(`/chat/${sessionId}/history`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(history.body.messages.length).toBeGreaterThanOrEqual(6); // 3 user + 3 assistant messages
      expect(history.body.total_messages).toBeGreaterThanOrEqual(6);
    });
  });
});

// Helper function to get test auth token
async function getTestAuthToken(): Promise<string> {
  // This would need to be implemented based on your auth system
  // For now, return a mock token or implement actual auth flow
  return 'mock-test-token';
}
