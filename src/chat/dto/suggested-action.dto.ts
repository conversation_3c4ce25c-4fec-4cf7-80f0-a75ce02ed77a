import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsOptional, IsObject } from 'class-validator';

export class SuggestedActionDto {
  @ApiProperty({
    description: 'Type of suggested action',
    enum: ['ask_question', 'show_entities', 'refine_search', 'get_recommendations'],
    example: 'ask_question',
  })
  @IsNotEmpty()
  @IsEnum(['ask_question', 'show_entities', 'refine_search', 'get_recommendations'])
  type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';

  @ApiProperty({
    description: 'Human-readable label for the action',
    example: 'Tell me more about your use case',
  })
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Additional data associated with the action',
    example: { question: 'What programming languages do you work with?' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: any;
}
