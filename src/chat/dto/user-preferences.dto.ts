import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsArray, IsString } from 'class-validator';

export class UserPreferencesDto {
  @ApiProperty({
    description: 'User budget preference for AI tools',
    enum: ['free', 'low', 'medium', 'high'],
    example: 'medium',
    required: false,
  })
  @IsOptional()
  @IsEnum(['free', 'low', 'medium', 'high'])
  budget?: 'free' | 'low' | 'medium' | 'high';

  @ApiProperty({
    description: 'User technical skill level',
    enum: ['beginner', 'intermediate', 'advanced'],
    example: 'intermediate',
    required: false,
  })
  @IsOptional()
  @IsEnum(['beginner', 'intermediate', 'advanced'])
  technical_level?: 'beginner' | 'intermediate' | 'advanced';

  @ApiProperty({
    description: 'Preferred AI tool categories',
    example: ['Developer Tools', 'Content Creation'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferred_categories?: string[];

  @ApiProperty({
    description: 'Categories to exclude from recommendations',
    example: ['Gaming', 'Entertainment'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excluded_categories?: string[];
}
