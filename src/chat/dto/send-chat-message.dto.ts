import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { UserPreferencesDto } from './user-preferences.dto';

export class SendChatMessageDto {
  @ApiProperty({
    description: 'The user message to send to the chatbot',
    example: 'I need help finding an AI tool for automated code documentation',
  })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Optional session ID to continue an existing conversation. If not provided, a new session will be created.',
    example: 'chat_123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  session_id?: string;

  @ApiProperty({
    description: 'User preferences to help personalize the conversation',
    type: UserPreferencesDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UserPreferencesDto)
  user_preferences?: UserPreferencesDto;

  @ApiProperty({
    description: 'Additional context or metadata for the message',
    example: { source: 'web', page: 'dashboard' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}
