import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsBoolean, IsArray, IsEnum, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SuggestedActionDto } from './suggested-action.dto';
import { DiscoveredEntityDto } from './discovered-entity.dto';

export class ChatResponseMetadataDto {
  @ApiProperty({
    description: 'Response time in milliseconds',
    example: 1250,
  })
  @IsNotEmpty()
  response_time: number;

  @ApiProperty({
    description: 'LLM provider used for this response',
    example: 'OPENAI',
  })
  @IsNotEmpty()
  @IsString()
  llm_provider: string;

  @ApiProperty({
    description: 'Number of tokens used (if available)',
    example: 150,
    required: false,
  })
  @IsOptional()
  tokens_used?: number;
}

export class ChatResponseDto {
  @ApiProperty({
    description: 'The chatbot response message',
    example: 'I\'d be happy to help you find the perfect AI tool for code documentation! Based on what you\'ve mentioned, there are several excellent options that could work well for your needs.',
  })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Session ID for this conversation',
    example: 'chat_123e4567-e89b-12d3-a456-426614174000',
  })
  @IsNotEmpty()
  @IsString()
  session_id: string;

  @ApiProperty({
    description: 'Current stage of the conversation',
    enum: ['greeting', 'discovery', 'refinement', 'recommendation', 'comparison'],
    example: 'discovery',
  })
  @IsNotEmpty()
  @IsEnum(['greeting', 'discovery', 'refinement', 'recommendation', 'comparison'])
  conversation_stage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';

  @ApiProperty({
    description: 'Suggested actions the user can take',
    type: [SuggestedActionDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SuggestedActionDto)
  suggested_actions?: SuggestedActionDto[];

  @ApiProperty({
    description: 'AI tools discovered during this conversation turn',
    type: [DiscoveredEntityDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DiscoveredEntityDto)
  discovered_entities?: DiscoveredEntityDto[];

  @ApiProperty({
    description: 'Follow-up questions to guide the conversation',
    example: ['What programming languages do you primarily work with?', 'Do you prefer free tools or are you open to paid options?'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  follow_up_questions?: string[];

  @ApiProperty({
    description: 'Whether the conversation is ready to transition to formal recommendations',
    example: false,
  })
  @IsNotEmpty()
  @IsBoolean()
  should_transition_to_recommendations: boolean;

  @ApiProperty({
    description: 'Response metadata including timing and provider information',
    type: ChatResponseMetadataDto,
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => ChatResponseMetadataDto)
  metadata: ChatResponseMetadataDto;

  @ApiProperty({
    description: 'Timestamp when the response was generated',
    example: '2024-06-20T01:00:00.000Z',
  })
  @IsNotEmpty()
  generated_at: Date;
}
