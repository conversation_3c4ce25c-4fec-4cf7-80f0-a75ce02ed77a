import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID, IsEnum, IsObject } from 'class-validator';

export class ChatMessageDto {
  @ApiProperty({
    description: 'Unique identifier for the message',
    example: 'msg_123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID(4)
  id?: string;

  @ApiProperty({
    description: 'Role of the message sender',
    enum: ['user', 'assistant', 'system'],
    example: 'user',
  })
  @IsNotEmpty()
  @IsEnum(['user', 'assistant', 'system'])
  role: 'user' | 'assistant' | 'system';

  @ApiProperty({
    description: 'Content of the message',
    example: 'I need help finding an AI tool for code documentation',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Timestamp when the message was created',
    example: '2024-06-20T01:00:00.000Z',
    required: false,
  })
  @IsOptional()
  timestamp?: Date;

  @ApiProperty({
    description: 'Additional metadata for the message',
    example: { source: 'web', userAgent: 'Mozilla/5.0...' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
