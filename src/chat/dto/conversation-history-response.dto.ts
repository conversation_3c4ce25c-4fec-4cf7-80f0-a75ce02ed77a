import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ChatMessageDto } from './chat-message.dto';

export class ConversationHistoryResponseDto {
  @ApiProperty({
    description: 'Session ID for this conversation',
    example: 'chat_123e4567-e89b-12d3-a456-426614174000',
  })
  @IsNotEmpty()
  @IsString()
  session_id: string;

  @ApiProperty({
    description: 'Current stage of the conversation',
    enum: ['greeting', 'discovery', 'refinement', 'recommendation', 'comparison'],
    example: 'discovery',
  })
  @IsNotEmpty()
  @IsEnum(['greeting', 'discovery', 'refinement', 'recommendation', 'comparison'])
  conversation_stage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';

  @ApiProperty({
    description: 'Array of messages in the conversation',
    type: [ChatMessageDto],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessageDto)
  messages: ChatMessageDto[];

  @ApiProperty({
    description: 'Total number of messages in the conversation',
    example: 8,
  })
  @IsNotEmpty()
  total_messages: number;

  @ApiProperty({
    description: 'Number of AI tools discovered in this conversation',
    example: 5,
  })
  @IsNotEmpty()
  discovered_entities_count: number;

  @ApiProperty({
    description: 'When the conversation was started',
    example: '2024-06-20T01:00:00.000Z',
  })
  @IsNotEmpty()
  started_at: Date;

  @ApiProperty({
    description: 'When the conversation was last active',
    example: '2024-06-20T01:15:00.000Z',
  })
  @IsNotEmpty()
  last_active_at: Date;
}
