import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, <PERSON>UUI<PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class DiscoveredEntityDto {
  @ApiProperty({
    description: 'Unique identifier of the discovered entity',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID(4)
  id: string;

  @ApiProperty({
    description: 'Name of the discovered entity',
    example: 'GitHub Copilot',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Relevance score for this entity (0.0 to 1.0)',
    example: 0.95,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  relevance_score: number;

  @ApiProperty({
    description: 'Explanation of why this entity is relevant',
    example: 'This tool is perfect for automated code documentation as it integrates directly with your IDE and understands your codebase context.',
  })
  @IsNotEmpty()
  @IsString()
  reason: string;
}
