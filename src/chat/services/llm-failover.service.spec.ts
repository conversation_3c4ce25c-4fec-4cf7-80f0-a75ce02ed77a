import { Test, TestingModule } from '@nestjs/testing';
import { LlmFailoverService } from './llm-failover.service';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { ILlmService, ConversationContext, ChatResponse, UserIntent } from '../../common/llm/interfaces/llm.service.interface';

describe('LlmFailoverService', () => {
  let service: LlmFailoverService;
  let llmFactoryService: jest.Mocked<LlmFactoryService>;
  let mockOpenAIService: jest.Mocked<ILlmService>;
  let mockGeminiService: jest.Mocked<ILlmService>;
  let mockAnthropicService: jest.Mocked<ILlmService>;

  const mockContext: ConversationContext = {
    sessionId: 'test-session',
    userId: 'user-123',
    messages: [],
    discoveredEntities: [],
    userPreferences: {},
    conversationStage: 'discovery',
    metadata: {
      startedAt: new Date(),
      lastActiveAt: new Date(),
      totalMessages: 2,
      entitiesShown: [],
    },
  };

  const mockChatResponse: ChatResponse = {
    message: 'Test response',
    intent: {
      type: 'discovery',
      confidence: 0.9,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    },
    shouldTransitionToRecommendations: false,
    conversationStage: 'discovery',
    metadata: {
      responseTime: 1000,
      llmProvider: 'OPENAI',
    },
  };

  const mockIntent: UserIntent = {
    type: 'discovery',
    confidence: 0.8,
    entities: [],
    categories: [],
    features: [],
    constraints: {},
  };

  beforeEach(async () => {
    mockOpenAIService = {
      getChatResponse: jest.fn(),
      classifyIntent: jest.fn(),
      generateFollowUpQuestions: jest.fn(),
      shouldTransitionToRecommendations: jest.fn(),
      getRecommendation: jest.fn(),
    };

    mockGeminiService = {
      getChatResponse: jest.fn(),
      classifyIntent: jest.fn(),
      generateFollowUpQuestions: jest.fn(),
      shouldTransitionToRecommendations: jest.fn(),
      getRecommendation: jest.fn(),
    };

    mockAnthropicService = {
      getChatResponse: jest.fn(),
      classifyIntent: jest.fn(),
      generateFollowUpQuestions: jest.fn(),
      shouldTransitionToRecommendations: jest.fn(),
      getRecommendation: jest.fn(),
    };

    const mockLlmFactoryService = {
      getAvailableProviders: jest.fn().mockReturnValue(['OPENAI', 'GOOGLE_GEMINI', 'ANTHROPIC']),
      getLlmServiceByProvider: jest.fn().mockImplementation((provider: string) => {
        switch (provider) {
          case 'OPENAI':
            return mockOpenAIService;
          case 'GOOGLE_GEMINI':
            return mockGeminiService;
          case 'ANTHROPIC':
            return mockAnthropicService;
          default:
            throw new Error(`Unknown provider: ${provider}`);
        }
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmFailoverService,
        {
          provide: LlmFactoryService,
          useValue: mockLlmFactoryService,
        },
      ],
    }).compile();

    service = module.get<LlmFailoverService>(LlmFailoverService);
    llmFactoryService = module.get(LlmFactoryService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getChatResponseWithFailover', () => {
    it('should return response from first healthy provider', async () => {
      mockOpenAIService.getChatResponse.mockResolvedValue(mockChatResponse);

      const result = await service.getChatResponseWithFailover(
        'Test message',
        mockContext,
      );

      expect(result).toEqual(mockChatResponse);
      expect(mockOpenAIService.getChatResponse).toHaveBeenCalledWith(
        'Test message',
        mockContext,
        undefined,
      );
      expect(mockGeminiService.getChatResponse).not.toHaveBeenCalled();
    });

    it('should failover to second provider when first fails', async () => {
      mockOpenAIService.getChatResponse.mockRejectedValue(new Error('OpenAI error'));
      mockGeminiService.getChatResponse.mockResolvedValue(mockChatResponse);

      const result = await service.getChatResponseWithFailover(
        'Test message',
        mockContext,
      );

      expect(result).toEqual(mockChatResponse);
      expect(mockOpenAIService.getChatResponse).toHaveBeenCalled();
      expect(mockGeminiService.getChatResponse).toHaveBeenCalled();
      expect(mockAnthropicService.getChatResponse).not.toHaveBeenCalled();
    });

    it('should try all providers before failing', async () => {
      mockOpenAIService.getChatResponse.mockRejectedValue(new Error('OpenAI error'));
      mockGeminiService.getChatResponse.mockRejectedValue(new Error('Gemini error'));
      mockAnthropicService.getChatResponse.mockRejectedValue(new Error('Anthropic error'));

      await expect(
        service.getChatResponseWithFailover('Test message', mockContext),
      ).rejects.toThrow('All LLM providers failed to generate chat response');

      expect(mockOpenAIService.getChatResponse).toHaveBeenCalled();
      expect(mockGeminiService.getChatResponse).toHaveBeenCalled();
      expect(mockAnthropicService.getChatResponse).toHaveBeenCalled();
    });

    it('should handle timeout errors', async () => {
      // Mock a long-running operation that should timeout
      mockOpenAIService.getChatResponse.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 35000)),
      );
      mockGeminiService.getChatResponse.mockResolvedValue(mockChatResponse);

      const result = await service.getChatResponseWithFailover(
        'Test message',
        mockContext,
      );

      expect(result).toEqual(mockChatResponse);
      expect(mockGeminiService.getChatResponse).toHaveBeenCalled();
    });
  });

  describe('classifyIntentWithFailover', () => {
    it('should return intent from first healthy provider', async () => {
      mockOpenAIService.classifyIntent.mockResolvedValue(mockIntent);

      const result = await service.classifyIntentWithFailover(
        'Test message',
        mockContext,
      );

      expect(result).toEqual(mockIntent);
      expect(mockOpenAIService.classifyIntent).toHaveBeenCalledWith(
        'Test message',
        mockContext,
      );
    });

    it('should return fallback intent when all providers fail', async () => {
      mockOpenAIService.classifyIntent.mockRejectedValue(new Error('OpenAI error'));
      mockGeminiService.classifyIntent.mockRejectedValue(new Error('Gemini error'));
      mockAnthropicService.classifyIntent.mockRejectedValue(new Error('Anthropic error'));

      const result = await service.classifyIntentWithFailover(
        'Test message',
        mockContext,
      );

      expect(result).toEqual({
        type: 'discovery',
        confidence: 0.5,
        entities: [],
        categories: [],
        features: [],
        constraints: {},
      });
    });
  });

  describe('generateFollowUpQuestionsWithFailover', () => {
    it('should return questions from first healthy provider', async () => {
      const mockQuestions = ['Question 1?', 'Question 2?'];
      mockOpenAIService.generateFollowUpQuestions.mockResolvedValue(mockQuestions);

      const result = await service.generateFollowUpQuestionsWithFailover(mockContext);

      expect(result).toEqual(mockQuestions);
      expect(mockOpenAIService.generateFollowUpQuestions).toHaveBeenCalledWith(mockContext);
    });

    it('should return fallback questions when all providers fail', async () => {
      mockOpenAIService.generateFollowUpQuestions.mockRejectedValue(new Error('OpenAI error'));
      mockGeminiService.generateFollowUpQuestions.mockRejectedValue(new Error('Gemini error'));
      mockAnthropicService.generateFollowUpQuestions.mockRejectedValue(new Error('Anthropic error'));

      const result = await service.generateFollowUpQuestionsWithFailover(mockContext);

      expect(result).toEqual([
        "What's your experience level with AI tools?",
        "Do you have a budget in mind?",
        "What's the main challenge you're trying to solve?"
      ]);
    });
  });

  describe('shouldTransitionToRecommendationsWithFailover', () => {
    it('should return transition decision from first healthy provider', async () => {
      const mockDecision = { shouldTransition: true, reason: 'Ready for recommendations' };
      mockOpenAIService.shouldTransitionToRecommendations.mockResolvedValue(mockDecision);

      const result = await service.shouldTransitionToRecommendationsWithFailover(mockContext);

      expect(result).toEqual(mockDecision);
      expect(mockOpenAIService.shouldTransitionToRecommendations).toHaveBeenCalledWith(mockContext);
    });

    it('should return fallback decision when all providers fail', async () => {
      mockOpenAIService.shouldTransitionToRecommendations.mockRejectedValue(new Error('OpenAI error'));
      mockGeminiService.shouldTransitionToRecommendations.mockRejectedValue(new Error('Gemini error'));
      mockAnthropicService.shouldTransitionToRecommendations.mockRejectedValue(new Error('Anthropic error'));

      const result = await service.shouldTransitionToRecommendationsWithFailover(mockContext);

      expect(result.shouldTransition).toBe(false);
      expect(result.reason).toContain('Need more conversation context');
    });

    it('should use heuristics for fallback transition decision', async () => {
      // Create context that should trigger transition
      const contextForTransition: ConversationContext = {
        ...mockContext,
        messages: new Array(7).fill(null).map((_, i) => ({
          id: `msg-${i}`,
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Message ${i}`,
          timestamp: new Date(),
        })),
        discoveredEntities: ['entity-1', 'entity-2', 'entity-3'],
        userPreferences: { budget: 'medium', technical_level: 'intermediate' },
        metadata: {
          ...mockContext.metadata,
          totalMessages: 7,
        },
      };

      mockOpenAIService.shouldTransitionToRecommendations.mockRejectedValue(new Error('All providers failed'));
      mockGeminiService.shouldTransitionToRecommendations.mockRejectedValue(new Error('All providers failed'));
      mockAnthropicService.shouldTransitionToRecommendations.mockRejectedValue(new Error('All providers failed'));

      const result = await service.shouldTransitionToRecommendationsWithFailover(contextForTransition);

      expect(result.shouldTransition).toBe(true);
      expect(result.reason).toContain('Sufficient conversation depth');
    });
  });

  describe('provider health tracking', () => {
    it('should track provider health status', () => {
      const healthStatus = service.getProviderHealthStatus();

      expect(healthStatus).toHaveProperty('OPENAI');
      expect(healthStatus).toHaveProperty('GOOGLE_GEMINI');
      expect(healthStatus).toHaveProperty('ANTHROPIC');

      // All providers should start as healthy
      expect(healthStatus.OPENAI.isHealthy).toBe(true);
      expect(healthStatus.GOOGLE_GEMINI.isHealthy).toBe(true);
      expect(healthStatus.ANTHROPIC.isHealthy).toBe(true);
    });

    it('should reset provider health manually', () => {
      service.resetProviderHealth('OPENAI');

      const healthStatus = service.getProviderHealthStatus();
      expect(healthStatus.OPENAI.isHealthy).toBe(true);
      expect(healthStatus.OPENAI.consecutiveFailures).toBe(0);
    });
  });
});
