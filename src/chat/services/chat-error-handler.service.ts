import { Injectable, Logger } from '@nestjs/common';
import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Centralized error handling service for chat operations
 * Provides graceful degradation and user-friendly error responses
 */
@Injectable()
export class ChatErrorHandlerService {
  private readonly logger = new Logger(ChatErrorHandlerService.name);

  /**
   * Handle LLM service errors with graceful fallbacks
   */
  handleLlmError(
    error: any,
    context: ConversationContext,
    userMessage: string,
  ): ChatResponseDto {
    this.logger.error('LLM service error', {
      error: error.message,
      stack: error.stack,
      sessionId: context.sessionId,
      userId: context.userId,
      conversationStage: context.conversationStage,
    });

    // Determine error type and provide appropriate fallback
    if (this.isRateLimitError(error)) {
      return this.createRateLimitFallbackResponse(context);
    }

    if (this.isApiKeyError(error)) {
      return this.createApiKeyFallbackResponse(context);
    }

    if (this.isNetworkError(error)) {
      return this.createNetworkFallbackResponse(context);
    }

    if (this.isQuotaExceededError(error)) {
      return this.createQuotaFallbackResponse(context);
    }

    // Generic LLM error fallback
    return this.createGenericLlmFallbackResponse(context, userMessage);
  }

  /**
   * Handle conversation state errors
   */
  handleConversationStateError(
    error: any,
    sessionId: string,
    userId: string,
  ): ChatResponseDto {
    this.logger.error('Conversation state error', {
      error: error.message,
      stack: error.stack,
      sessionId,
      userId,
    });

    return {
      message: "I apologize, but I'm having trouble accessing our conversation history. Let's start fresh - how can I help you find the perfect AI tools today?",
      session_id: sessionId,
      conversation_stage: 'greeting',
      should_transition_to_recommendations: false,
      follow_up_questions: [
        "What type of AI tools are you looking for?",
        "What's your main use case or project?",
        "Are you looking for free or paid solutions?"
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  /**
   * Handle entity discovery errors
   */
  handleEntityDiscoveryError(
    error: any,
    context: ConversationContext,
    userMessage: string,
  ): ChatResponseDto {
    this.logger.error('Entity discovery error', {
      error: error.message,
      stack: error.stack,
      sessionId: context.sessionId,
      userMessage,
    });

    return {
      message: "I'm having some trouble searching our AI tools database right now, but I can still help you! Could you tell me more specifically what you're looking for? For example, are you interested in tools for content creation, data analysis, coding assistance, or something else?",
      session_id: context.sessionId,
      conversation_stage: context.conversationStage,
      should_transition_to_recommendations: false,
      follow_up_questions: [
        "What specific task do you want to accomplish?",
        "What industry or field are you working in?",
        "Do you have any budget constraints?"
      ],
      suggested_actions: [
        {
          type: 'ask_question',
          label: 'Tell me about your specific use case',
          data: { prompt: 'Please describe what you want to accomplish in more detail' }
        }
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  /**
   * Handle authentication/authorization errors
   */
  handleAuthError(error: any, sessionId?: string): never {
    this.logger.error('Authentication error', {
      error: error.message,
      sessionId,
    });

    throw new Error('Authentication required. Please log in to continue using the chat service.');
  }

  /**
   * Handle validation errors
   */
  handleValidationError(error: any, sessionId?: string): never {
    this.logger.error('Validation error', {
      error: error.message,
      sessionId,
    });

    throw new Error('Invalid input provided. Please check your message and try again.');
  }

  /**
   * Create a comprehensive error response for critical failures
   */
  createCriticalErrorFallback(
    sessionId: string,
    error: any,
  ): ChatResponseDto {
    this.logger.error('Critical chat service error', {
      error: error.message,
      stack: error.stack,
      sessionId,
    });

    return {
      message: "I'm experiencing some technical difficulties right now. While I work on getting back to full capacity, you might want to try our recommendations endpoint directly, or feel free to browse our AI tools catalog. I apologize for the inconvenience!",
      session_id: sessionId,
      conversation_stage: 'greeting',
      should_transition_to_recommendations: true,
      suggested_actions: [
        {
          type: 'get_recommendations',
          label: 'Get AI tool recommendations',
          data: { endpoint: '/recommendations' }
        }
      ],
      follow_up_questions: [
        "Would you like to try our recommendations feature instead?",
        "Can you describe what you're looking for in a few words?"
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  // Private helper methods for error detection
  private isRateLimitError(error: any): boolean {
    return error.message?.includes('rate limit') || 
           error.message?.includes('too many requests') ||
           error.status === 429;
  }

  private isApiKeyError(error: any): boolean {
    return error.message?.includes('API key') ||
           error.message?.includes('authentication') ||
           error.message?.includes('unauthorized') ||
           error.status === 401;
  }

  private isNetworkError(error: any): boolean {
    return error.code === 'ECONNREFUSED' ||
           error.code === 'ENOTFOUND' ||
           error.code === 'ETIMEDOUT' ||
           error.message?.includes('network') ||
           error.message?.includes('connection');
  }

  private isQuotaExceededError(error: any): boolean {
    return error.message?.includes('quota') ||
           error.message?.includes('billing') ||
           error.message?.includes('usage limit');
  }

  // Fallback response creators
  private createRateLimitFallbackResponse(context: ConversationContext): ChatResponseDto {
    return {
      message: "I'm getting a lot of requests right now! Let me take a moment to process your message. In the meantime, could you tell me more about what specific type of AI tool you're looking for?",
      session_id: context.sessionId,
      conversation_stage: context.conversationStage,
      should_transition_to_recommendations: false,
      follow_up_questions: [
        "What's your primary use case?",
        "Are you looking for free or paid tools?",
        "What's your technical experience level?"
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'RATE_LIMITED_FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  private createApiKeyFallbackResponse(context: ConversationContext): ChatResponseDto {
    return {
      message: "I'm having some technical issues with my AI processing right now. But don't worry - I can still help you find great AI tools! Could you describe what you're trying to accomplish?",
      session_id: context.sessionId,
      conversation_stage: context.conversationStage,
      should_transition_to_recommendations: true,
      suggested_actions: [
        {
          type: 'get_recommendations',
          label: 'Browse AI tools directly',
          data: { endpoint: '/recommendations' }
        }
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'API_KEY_FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  private createNetworkFallbackResponse(context: ConversationContext): ChatResponseDto {
    return {
      message: "I'm experiencing some connectivity issues, but I'm still here to help! While I work on reconnecting, could you tell me about your AI tool needs? I might be able to point you in the right direction.",
      session_id: context.sessionId,
      conversation_stage: context.conversationStage,
      should_transition_to_recommendations: false,
      follow_up_questions: [
        "What type of work are you trying to automate?",
        "Are you looking for tools for a specific industry?",
        "Do you need help with content, code, data, or something else?"
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'NETWORK_FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  private createQuotaFallbackResponse(context: ConversationContext): ChatResponseDto {
    return {
      message: "I've reached my processing limit for now, but I can still assist you! Let's try a different approach - you can browse our AI tools directly or tell me specifically what you're looking for and I'll do my best to help.",
      session_id: context.sessionId,
      conversation_stage: context.conversationStage,
      should_transition_to_recommendations: true,
      suggested_actions: [
        {
          type: 'get_recommendations',
          label: 'Get recommendations directly',
          data: { endpoint: '/recommendations' }
        }
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'QUOTA_FALLBACK',
      },
      generated_at: new Date(),
    };
  }

  private createGenericLlmFallbackResponse(
    context: ConversationContext,
    userMessage: string,
  ): ChatResponseDto {
    // Try to provide a contextual response based on conversation stage
    const stageResponses = {
      greeting: "Welcome! I'm here to help you discover amazing AI tools. What kind of tasks are you looking to accomplish?",
      discovery: "I'm having a small technical hiccup, but let's keep exploring! What specific features are most important to you in an AI tool?",
      refinement: "Let me help you narrow down your options. Based on what we've discussed, what's your top priority - ease of use, specific features, or budget?",
      recommendation: "I want to make sure I give you the best recommendations. Could you summarize your key requirements one more time?",
      comparison: "I'm having trouble processing comparisons right now, but I can still help you evaluate your options. What criteria matter most to you?"
    };

    return {
      message: stageResponses[context.conversationStage] || stageResponses.discovery,
      session_id: context.sessionId,
      conversation_stage: context.conversationStage,
      should_transition_to_recommendations: context.conversationStage === 'recommendation',
      follow_up_questions: [
        "What's your main goal with AI tools?",
        "What's your budget range?",
        "How technical do you want the solution to be?"
      ],
      metadata: {
        response_time: 0,
        llm_provider: 'GENERIC_FALLBACK',
      },
      generated_at: new Date(),
    };
  }
}
