import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LRUCache } from 'lru-cache';
import { ChatResponse, UserIntent } from '../../common/llm/interfaces/llm.service.interface';

/**
 * Caching service for chat responses and computed data
 * Improves performance by caching expensive operations
 */
@Injectable()
export class ChatCacheService {
  private readonly logger = new Logger(ChatCacheService.name);
  
  // Different caches for different types of data
  private readonly intentCache: LRUCache<string, UserIntent>;
  private readonly followUpCache: LRUCache<string, string[]>;
  private readonly entitySearchCache: LRUCache<string, any[]>;
  private readonly responseCache: LRUCache<string, ChatResponse>;

  // Cache configuration
  private readonly config = {
    intentCacheTtl: this.configService.get<number>('CHAT_INTENT_CACHE_TTL_MS', 300000), // 5 minutes
    followUpCacheTtl: this.configService.get<number>('CHAT_FOLLOWUP_CACHE_TTL_MS', 600000), // 10 minutes
    entitySearchCacheTtl: this.configService.get<number>('CHAT_ENTITY_CACHE_TTL_MS', 1800000), // 30 minutes
    responseCacheTtl: this.configService.get<number>('CHAT_RESPONSE_CACHE_TTL_MS', 60000), // 1 minute
    maxCacheSize: this.configService.get<number>('CHAT_CACHE_MAX_SIZE', 1000),
  };

  constructor(private readonly configService: ConfigService) {
    // Initialize intent classification cache
    this.intentCache = new LRUCache<string, UserIntent>({
      max: this.config.maxCacheSize,
      ttl: this.config.intentCacheTtl,
      updateAgeOnGet: true,
    });

    // Initialize follow-up questions cache
    this.followUpCache = new LRUCache<string, string[]>({
      max: this.config.maxCacheSize,
      ttl: this.config.followUpCacheTtl,
      updateAgeOnGet: true,
    });

    // Initialize entity search cache
    this.entitySearchCache = new LRUCache<string, any[]>({
      max: this.config.maxCacheSize,
      ttl: this.config.entitySearchCacheTtl,
      updateAgeOnGet: true,
    });

    // Initialize response cache (shorter TTL for dynamic content)
    this.responseCache = new LRUCache<string, ChatResponse>({
      max: this.config.maxCacheSize / 2, // Smaller cache for full responses
      ttl: this.config.responseCacheTtl,
      updateAgeOnGet: false, // Don't update age for responses
    });

    this.logger.log('Chat cache service initialized with LRU caches');
  }

  /**
   * Cache and retrieve intent classifications
   */
  async getOrSetIntent(
    key: string,
    computeFn: () => Promise<UserIntent>,
  ): Promise<UserIntent> {
    const cached = this.intentCache.get(key);
    if (cached) {
      this.logger.debug(`Intent cache hit for key: ${key}`);
      return cached;
    }

    this.logger.debug(`Intent cache miss for key: ${key}`);
    const result = await computeFn();
    this.intentCache.set(key, result);
    return result;
  }

  /**
   * Cache and retrieve follow-up questions
   */
  async getOrSetFollowUpQuestions(
    key: string,
    computeFn: () => Promise<string[]>,
  ): Promise<string[]> {
    const cached = this.followUpCache.get(key);
    if (cached) {
      this.logger.debug(`Follow-up cache hit for key: ${key}`);
      return cached;
    }

    this.logger.debug(`Follow-up cache miss for key: ${key}`);
    const result = await computeFn();
    this.followUpCache.set(key, result);
    return result;
  }

  /**
   * Cache and retrieve entity search results
   */
  async getOrSetEntitySearch(
    query: string,
    computeFn: () => Promise<any[]>,
  ): Promise<any[]> {
    const key = this.normalizeSearchQuery(query);
    const cached = this.entitySearchCache.get(key);
    if (cached) {
      this.logger.debug(`Entity search cache hit for query: ${query}`);
      return cached;
    }

    this.logger.debug(`Entity search cache miss for query: ${query}`);
    const result = await computeFn();
    this.entitySearchCache.set(key, result);
    return result;
  }

  /**
   * Cache and retrieve full chat responses (for identical contexts)
   */
  async getOrSetChatResponse(
    key: string,
    computeFn: () => Promise<ChatResponse>,
  ): Promise<ChatResponse> {
    const cached = this.responseCache.get(key);
    if (cached) {
      this.logger.debug(`Response cache hit for key: ${key}`);
      // Update timestamp for cached response
      return {
        ...cached,
        metadata: {
          ...cached.metadata,
          responseTime: 0, // Cached response
        },
      };
    }

    this.logger.debug(`Response cache miss for key: ${key}`);
    const result = await computeFn();
    this.responseCache.set(key, result);
    return result;
  }

  /**
   * Generate cache key for intent classification
   */
  generateIntentKey(message: string, contextHash: string): string {
    return `intent:${this.hashString(message)}:${contextHash}`;
  }

  /**
   * Generate cache key for follow-up questions
   */
  generateFollowUpKey(
    conversationStage: string,
    entitiesCount: number,
    preferencesHash: string,
  ): string {
    return `followup:${conversationStage}:${entitiesCount}:${preferencesHash}`;
  }

  /**
   * Generate cache key for chat responses
   */
  generateResponseKey(
    message: string,
    contextHash: string,
    entitiesHash: string,
  ): string {
    return `response:${this.hashString(message)}:${contextHash}:${entitiesHash}`;
  }

  /**
   * Generate hash for conversation context
   */
  generateContextHash(context: any): string {
    const relevantContext = {
      stage: context.conversationStage,
      preferences: context.userPreferences,
      entitiesCount: context.discoveredEntities?.length || 0,
      messageCount: context.messages?.length || 0,
    };
    return this.hashString(JSON.stringify(relevantContext));
  }

  /**
   * Generate hash for entities array
   */
  generateEntitiesHash(entities: any[]): string {
    const entityIds = entities.map(e => e.id).sort();
    return this.hashString(JSON.stringify(entityIds));
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): any {
    return {
      intent: {
        size: this.intentCache.size,
        maxSize: this.intentCache.max,
        hitRate: this.calculateHitRate(this.intentCache),
      },
      followUp: {
        size: this.followUpCache.size,
        maxSize: this.followUpCache.max,
        hitRate: this.calculateHitRate(this.followUpCache),
      },
      entitySearch: {
        size: this.entitySearchCache.size,
        maxSize: this.entitySearchCache.max,
        hitRate: this.calculateHitRate(this.entitySearchCache),
      },
      response: {
        size: this.responseCache.size,
        maxSize: this.responseCache.max,
        hitRate: this.calculateHitRate(this.responseCache),
      },
      config: this.config,
    };
  }

  /**
   * Clear all caches
   */
  clearAllCaches(): void {
    this.intentCache.clear();
    this.followUpCache.clear();
    this.entitySearchCache.clear();
    this.responseCache.clear();
    this.logger.log('All caches cleared');
  }

  /**
   * Clear specific cache
   */
  clearCache(cacheType: 'intent' | 'followUp' | 'entitySearch' | 'response'): void {
    switch (cacheType) {
      case 'intent':
        this.intentCache.clear();
        break;
      case 'followUp':
        this.followUpCache.clear();
        break;
      case 'entitySearch':
        this.entitySearchCache.clear();
        break;
      case 'response':
        this.responseCache.clear();
        break;
    }
    this.logger.log(`${cacheType} cache cleared`);
  }

  /**
   * Warm up caches with common queries
   */
  async warmUpCaches(commonQueries: string[]): Promise<void> {
    this.logger.log(`Warming up caches with ${commonQueries.length} common queries`);
    
    for (const query of commonQueries) {
      try {
        // Pre-populate entity search cache
        const normalizedQuery = this.normalizeSearchQuery(query);
        // You would call your actual entity search here
        // this.entitySearchCache.set(normalizedQuery, []);
        
        // Pre-populate intent cache for common patterns
        const intentKey = this.generateIntentKey(query, 'common');
        // You would call your actual intent classification here
        // this.intentCache.set(intentKey, defaultIntent);
        
      } catch (error) {
        this.logger.warn(`Failed to warm up cache for query: ${query}`, error);
      }
    }
  }

  // Private helper methods
  private hashString(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  private normalizeSearchQuery(query: string): string {
    return query
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '');
  }

  private calculateHitRate(cache: LRUCache<any, any>): number {
    // This is a simplified hit rate calculation
    // In a real implementation, you'd track hits and misses
    return cache.size > 0 ? Math.min(cache.size / cache.max * 100, 100) : 0;
  }
}
