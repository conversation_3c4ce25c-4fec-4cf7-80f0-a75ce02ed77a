import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Service for monitoring and optimizing chat performance
 * Tracks metrics, response times, and system health
 */
@Injectable()
export class ChatPerformanceMonitorService {
  private readonly logger = new Logger(ChatPerformanceMonitorService.name);
  
  // Performance metrics storage
  private metrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    responseTimes: [] as number[],
    llmProviderUsage: new Map<string, number>(),
    conversationStageDistribution: new Map<string, number>(),
    errorTypes: new Map<string, number>(),
    peakConcurrentSessions: 0,
    currentActiveSessions: 0,
  };

  // Performance thresholds
  private readonly thresholds = {
    maxResponseTime: this.configService.get<number>('CHAT_MAX_RESPONSE_TIME_MS', 5000),
    warningResponseTime: this.configService.get<number>('CHAT_WARNING_RESPONSE_TIME_MS', 3000),
    maxConcurrentSessions: this.configService.get<number>('CHAT_MAX_CONCURRENT_SESSIONS', 1000),
    metricsRetentionCount: this.configService.get<number>('CHAT_METRICS_RETENTION_COUNT', 1000),
  };

  constructor(private readonly configService: ConfigService) {
    // Start periodic metrics cleanup
    setInterval(() => this.cleanupOldMetrics(), 300000); // Every 5 minutes
  }

  /**
   * Record the start of a chat request
   */
  recordRequestStart(sessionId: string): string {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.metrics.totalRequests++;
    this.metrics.currentActiveSessions++;
    
    if (this.metrics.currentActiveSessions > this.metrics.peakConcurrentSessions) {
      this.metrics.peakConcurrentSessions = this.metrics.currentActiveSessions;
    }

    this.logger.debug(`Request started: ${requestId}, Active sessions: ${this.metrics.currentActiveSessions}`);
    return requestId;
  }

  /**
   * Record the completion of a chat request
   */
  recordRequestComplete(
    requestId: string,
    responseTimeMs: number,
    success: boolean,
    llmProvider: string,
    conversationStage: string,
    error?: string,
  ): void {
    this.metrics.currentActiveSessions = Math.max(0, this.metrics.currentActiveSessions - 1);
    
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
      if (error) {
        this.metrics.errorTypes.set(error, (this.metrics.errorTypes.get(error) || 0) + 1);
      }
    }

    // Record response time
    this.metrics.responseTimes.push(responseTimeMs);
    this.updateAverageResponseTime();

    // Record LLM provider usage
    this.metrics.llmProviderUsage.set(
      llmProvider,
      (this.metrics.llmProviderUsage.get(llmProvider) || 0) + 1,
    );

    // Record conversation stage distribution
    this.metrics.conversationStageDistribution.set(
      conversationStage,
      (this.metrics.conversationStageDistribution.get(conversationStage) || 0) + 1,
    );

    // Log performance warnings
    if (responseTimeMs > this.thresholds.warningResponseTime) {
      this.logger.warn(
        `Slow response detected: ${responseTimeMs}ms (threshold: ${this.thresholds.warningResponseTime}ms)`,
        {
          requestId,
          responseTimeMs,
          llmProvider,
          conversationStage,
        },
      );
    }

    if (responseTimeMs > this.thresholds.maxResponseTime) {
      this.logger.error(
        `Response time exceeded maximum threshold: ${responseTimeMs}ms`,
        {
          requestId,
          responseTimeMs,
          llmProvider,
          conversationStage,
        },
      );
    }

    this.logger.debug(`Request completed: ${requestId}, Response time: ${responseTimeMs}ms`);
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): any {
    const responseTimes = this.metrics.responseTimes;
    const sortedTimes = [...responseTimes].sort((a, b) => a - b);
    
    return {
      requests: {
        total: this.metrics.totalRequests,
        successful: this.metrics.successfulRequests,
        failed: this.metrics.failedRequests,
        successRate: this.metrics.totalRequests > 0 
          ? (this.metrics.successfulRequests / this.metrics.totalRequests) * 100 
          : 0,
      },
      performance: {
        averageResponseTime: this.metrics.averageResponseTime,
        medianResponseTime: this.getPercentile(sortedTimes, 50),
        p95ResponseTime: this.getPercentile(sortedTimes, 95),
        p99ResponseTime: this.getPercentile(sortedTimes, 99),
        minResponseTime: sortedTimes.length > 0 ? sortedTimes[0] : 0,
        maxResponseTime: sortedTimes.length > 0 ? sortedTimes[sortedTimes.length - 1] : 0,
      },
      sessions: {
        currentActive: this.metrics.currentActiveSessions,
        peakConcurrent: this.metrics.peakConcurrentSessions,
      },
      llmProviders: Object.fromEntries(this.metrics.llmProviderUsage),
      conversationStages: Object.fromEntries(this.metrics.conversationStageDistribution),
      errors: Object.fromEntries(this.metrics.errorTypes),
      thresholds: this.thresholds,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get performance health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Check response time health
    if (this.metrics.averageResponseTime > this.thresholds.maxResponseTime) {
      issues.push(`Average response time (${this.metrics.averageResponseTime}ms) exceeds maximum threshold`);
      recommendations.push('Consider scaling LLM providers or optimizing prompts');
      status = 'critical';
    } else if (this.metrics.averageResponseTime > this.thresholds.warningResponseTime) {
      issues.push(`Average response time (${this.metrics.averageResponseTime}ms) exceeds warning threshold`);
      recommendations.push('Monitor LLM provider performance and consider optimization');
      if (status === 'healthy') status = 'warning';
    }

    // Check error rate
    const errorRate = this.metrics.totalRequests > 0 
      ? (this.metrics.failedRequests / this.metrics.totalRequests) * 100 
      : 0;

    if (errorRate > 10) {
      issues.push(`High error rate: ${errorRate.toFixed(2)}%`);
      recommendations.push('Investigate error patterns and improve error handling');
      status = 'critical';
    } else if (errorRate > 5) {
      issues.push(`Elevated error rate: ${errorRate.toFixed(2)}%`);
      recommendations.push('Monitor error patterns and consider preventive measures');
      if (status === 'healthy') status = 'warning';
    }

    // Check concurrent sessions
    if (this.metrics.currentActiveSessions > this.thresholds.maxConcurrentSessions * 0.9) {
      issues.push(`High concurrent session load: ${this.metrics.currentActiveSessions}`);
      recommendations.push('Consider implementing session queuing or scaling infrastructure');
      if (status === 'healthy') status = 'warning';
    }

    // Check LLM provider distribution
    const totalProviderUsage = Array.from(this.metrics.llmProviderUsage.values()).reduce((a, b) => a + b, 0);
    if (totalProviderUsage > 0) {
      const maxProviderUsage = Math.max(...this.metrics.llmProviderUsage.values());
      const providerConcentration = (maxProviderUsage / totalProviderUsage) * 100;
      
      if (providerConcentration > 80) {
        issues.push(`High concentration on single LLM provider: ${providerConcentration.toFixed(1)}%`);
        recommendations.push('Ensure LLM provider failover is working correctly');
        if (status === 'healthy') status = 'warning';
      }
    }

    return { status, issues, recommendations };
  }

  /**
   * Reset metrics (useful for testing or periodic resets)
   */
  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      responseTimes: [],
      llmProviderUsage: new Map(),
      conversationStageDistribution: new Map(),
      errorTypes: new Map(),
      peakConcurrentSessions: 0,
      currentActiveSessions: 0,
    };

    this.logger.log('Performance metrics reset');
  }

  /**
   * Get performance recommendations based on current metrics
   */
  getPerformanceRecommendations(): string[] {
    const recommendations: string[] = [];
    const metrics = this.getMetrics();

    // Response time recommendations
    if (metrics.performance.averageResponseTime > 2000) {
      recommendations.push('Consider implementing response caching for common queries');
      recommendations.push('Optimize LLM prompts to reduce token usage');
    }

    if (metrics.performance.p95ResponseTime > 5000) {
      recommendations.push('Implement request timeout and graceful degradation');
      recommendations.push('Consider adding more LLM provider options');
    }

    // Error rate recommendations
    if (metrics.requests.successRate < 95) {
      recommendations.push('Improve error handling and fallback mechanisms');
      recommendations.push('Add more comprehensive input validation');
    }

    // Provider distribution recommendations
    const providerCount = Object.keys(metrics.llmProviders).length;
    if (providerCount < 2) {
      recommendations.push('Add additional LLM providers for better reliability');
    }

    // Session management recommendations
    if (metrics.sessions.peakConcurrent > 500) {
      recommendations.push('Consider implementing session pooling or queuing');
      recommendations.push('Monitor memory usage and implement session cleanup');
    }

    return recommendations;
  }

  // Private helper methods
  private updateAverageResponseTime(): void {
    const times = this.metrics.responseTimes;
    if (times.length > 0) {
      this.metrics.averageResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
    }
  }

  private getPercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  private cleanupOldMetrics(): void {
    // Keep only the most recent response times to prevent memory bloat
    if (this.metrics.responseTimes.length > this.thresholds.metricsRetentionCount) {
      this.metrics.responseTimes = this.metrics.responseTimes.slice(-this.thresholds.metricsRetentionCount);
      this.updateAverageResponseTime();
      this.logger.debug(`Cleaned up old metrics, retained ${this.metrics.responseTimes.length} response times`);
    }
  }
}
