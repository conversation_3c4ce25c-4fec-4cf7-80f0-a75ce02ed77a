import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './chat.service';
import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { EntitiesService } from '../../entities/entities.service';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { ILlmService, ConversationContext, ChatResponse } from '../../common/llm/interfaces/llm.service.interface';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';

describe('ChatService', () => {
  let service: ChatService;
  let conversationManager: jest.Mocked<ConversationManagerService>;
  let chatErrorHandler: jest.Mocked<ChatErrorHandlerService>;
  let llmFailover: jest.Mocked<LlmFailoverService>;
  let entitiesService: jest.Mocked<EntitiesService>;
  let llmService: jest.Mocked<ILlmService>;

  const mockContext: ConversationContext = {
    sessionId: 'chat_test-session',
    userId: 'user-123',
    messages: [],
    discoveredEntities: [],
    userPreferences: {},
    conversationStage: 'greeting',
    metadata: {
      startedAt: new Date(),
      lastActiveAt: new Date(),
      totalMessages: 0,
      entitiesShown: [],
    },
  };

  const mockChatResponse: ChatResponse = {
    message: 'Hello! How can I help you find AI tools?',
    intent: {
      type: 'discovery',
      confidence: 0.9,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    },
    shouldTransitionToRecommendations: false,
    conversationStage: 'greeting',
    metadata: {
      responseTime: 1000,
      llmProvider: 'OPENAI',
    },
  };

  beforeEach(async () => {
    const mockConversationManager = {
      getOrCreateConversation: jest.fn(),
      updateUserPreferences: jest.fn(),
      addMessage: jest.fn(),
      addDiscoveredEntities: jest.fn(),
      updateConversationStage: jest.fn(),
      getConversationHistory: jest.fn(),
      endConversation: jest.fn(),
      getUserActiveSessions: jest.fn(),
    };

    const mockChatErrorHandler = {
      handleLlmError: jest.fn(),
      handleConversationStateError: jest.fn(),
      handleEntityDiscoveryError: jest.fn(),
      createCriticalErrorFallback: jest.fn(),
    };

    const mockLlmFailover = {
      getChatResponseWithFailover: jest.fn(),
    };

    const mockEntitiesService = {
      vectorSearch: jest.fn(),
    };

    const mockLlmService = {
      getChatResponse: jest.fn(),
      classifyIntent: jest.fn(),
      generateFollowUpQuestions: jest.fn(),
      shouldTransitionToRecommendations: jest.fn(),
      getRecommendation: jest.fn(),
    };

    const mockLlmFactoryService = {
      getLlmService: jest.fn(),
      getLlmServiceByProvider: jest.fn(),
      getAvailableProviders: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        {
          provide: ConversationManagerService,
          useValue: mockConversationManager,
        },
        {
          provide: ChatErrorHandlerService,
          useValue: mockChatErrorHandler,
        },
        {
          provide: LlmFailoverService,
          useValue: mockLlmFailover,
        },
        {
          provide: EntitiesService,
          useValue: mockEntitiesService,
        },
        {
          provide: 'ILlmService',
          useValue: mockLlmService,
        },
        {
          provide: LlmFactoryService,
          useValue: mockLlmFactoryService,
        },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);
    conversationManager = module.get(ConversationManagerService);
    chatErrorHandler = module.get(ChatErrorHandlerService);
    llmFailover = module.get(LlmFailoverService);
    entitiesService = module.get(EntitiesService);
    llmService = module.get('ILlmService');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendMessage', () => {
    const sendMessageDto: SendChatMessageDto = {
      message: 'I need help finding AI tools',
    };

    it('should process a chat message successfully', async () => {
      conversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      conversationManager.addMessage.mockResolvedValue(mockContext);
      entitiesService.vectorSearch.mockResolvedValue([]);
      llmFailover.getChatResponseWithFailover.mockResolvedValue(mockChatResponse);

      const result = await service.sendMessage('user-123', sendMessageDto);

      expect(result).toBeDefined();
      expect(result.message).toBe(mockChatResponse.message);
      expect(result.session_id).toBe(mockContext.sessionId);
      expect(conversationManager.getOrCreateConversation).toHaveBeenCalledWith(
        'user-123',
        undefined,
      );
    });

    it('should handle conversation state errors gracefully', async () => {
      const error = new Error('Conversation state error');
      const fallbackResponse = {
        message: 'Fallback response',
        session_id: 'fallback-session',
        conversation_stage: 'greeting' as const,
        should_transition_to_recommendations: false,
        metadata: {
          response_time: 0,
          llm_provider: 'FALLBACK',
        },
        generated_at: new Date(),
      };

      conversationManager.getOrCreateConversation.mockRejectedValue(error);
      chatErrorHandler.handleConversationStateError.mockReturnValue(fallbackResponse);

      const result = await service.sendMessage('user-123', sendMessageDto);

      expect(result).toEqual(fallbackResponse);
      expect(chatErrorHandler.handleConversationStateError).toHaveBeenCalledWith(
        error,
        undefined,
        'user-123',
      );
    });

    it('should handle LLM errors with fallback', async () => {
      const llmError = new Error('LLM service error');
      const fallbackResponse = {
        message: 'LLM fallback response',
        session_id: mockContext.sessionId,
        conversation_stage: 'greeting' as const,
        should_transition_to_recommendations: false,
        metadata: {
          response_time: 0,
          llm_provider: 'FALLBACK',
        },
        generated_at: new Date(),
      };

      conversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      conversationManager.addMessage.mockResolvedValue(mockContext);
      entitiesService.vectorSearch.mockResolvedValue([]);
      llmFailover.getChatResponseWithFailover.mockRejectedValue(llmError);
      chatErrorHandler.handleLlmError.mockReturnValue(fallbackResponse);

      const result = await service.sendMessage('user-123', sendMessageDto);

      expect(result).toEqual(fallbackResponse);
      expect(chatErrorHandler.handleLlmError).toHaveBeenCalledWith(
        llmError,
        mockContext,
        sendMessageDto.message,
      );
    });

    it('should update user preferences when provided', async () => {
      const sendMessageWithPrefs: SendChatMessageDto = {
        message: 'Test message',
        user_preferences: {
          budget: 'medium',
          technical_level: 'intermediate',
        },
      };

      conversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      conversationManager.addMessage.mockResolvedValue(mockContext);
      entitiesService.vectorSearch.mockResolvedValue([]);
      llmFailover.getChatResponseWithFailover.mockResolvedValue(mockChatResponse);

      await service.sendMessage('user-123', sendMessageWithPrefs);

      expect(conversationManager.updateUserPreferences).toHaveBeenCalledWith(
        mockContext.sessionId,
        sendMessageWithPrefs.user_preferences,
      );
    });

    it('should handle entity discovery errors gracefully', async () => {
      conversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      conversationManager.addMessage.mockResolvedValue(mockContext);
      entitiesService.vectorSearch.mockRejectedValue(new Error('Vector search error'));
      llmFailover.getChatResponseWithFailover.mockResolvedValue(mockChatResponse);

      const result = await service.sendMessage('user-123', sendMessageDto);

      expect(result).toBeDefined();
      // Should continue with empty entities array
      expect(llmFailover.getChatResponseWithFailover).toHaveBeenCalledWith(
        sendMessageDto.message,
        mockContext,
        [],
      );
    });

    it('should handle critical errors with fallback response', async () => {
      const criticalError = new Error('Critical system error');
      const criticalFallback = {
        message: 'Critical error fallback',
        session_id: 'fallback-session',
        conversation_stage: 'greeting' as const,
        should_transition_to_recommendations: true,
        metadata: {
          response_time: 0,
          llm_provider: 'FALLBACK',
        },
        generated_at: new Date(),
      };

      conversationManager.getOrCreateConversation.mockRejectedValue(criticalError);
      chatErrorHandler.handleConversationStateError.mockImplementation(() => {
        throw criticalError;
      });
      chatErrorHandler.createCriticalErrorFallback.mockReturnValue(criticalFallback);

      const result = await service.sendMessage('user-123', sendMessageDto);

      expect(result).toEqual(criticalFallback);
      expect(chatErrorHandler.createCriticalErrorFallback).toHaveBeenCalled();
    });
  });

  describe('getConversationHistory', () => {
    it('should get conversation history successfully', async () => {
      const mockMessages = [
        {
          id: 'msg-1',
          role: 'user' as const,
          content: 'Hello',
          timestamp: new Date(),
        },
      ];

      conversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      conversationManager.getConversationHistory.mockResolvedValue(mockMessages);

      const result = await service.getConversationHistory(
        'user-123',
        'chat_test-session',
        { limit: 20 },
      );

      expect(result).toBeDefined();
      expect(result.session_id).toBe('chat_test-session');
      expect(result.messages).toEqual(mockMessages);
    });

    it('should throw error for unauthorized access', async () => {
      const unauthorizedContext = { ...mockContext, userId: 'other-user' };
      conversationManager.getOrCreateConversation.mockResolvedValue(unauthorizedContext);

      await expect(
        service.getConversationHistory('user-123', 'chat_test-session', { limit: 20 }),
      ).rejects.toThrow('Authentication required');
    });
  });

  describe('endConversation', () => {
    it('should end conversation successfully', async () => {
      conversationManager.getOrCreateConversation.mockResolvedValue(mockContext);
      conversationManager.endConversation.mockResolvedValue(undefined);

      await service.endConversation('user-123', 'chat_test-session');

      expect(conversationManager.endConversation).toHaveBeenCalledWith('chat_test-session');
    });

    it('should throw error for unauthorized access', async () => {
      const unauthorizedContext = { ...mockContext, userId: 'other-user' };
      conversationManager.getOrCreateConversation.mockResolvedValue(unauthorizedContext);

      await expect(
        service.endConversation('user-123', 'chat_test-session'),
      ).rejects.toThrow('Session does not belong to the user');
    });
  });

  describe('getUserActiveSessions', () => {
    it('should get user active sessions', async () => {
      const mockSessions = ['session-1', 'session-2'];
      conversationManager.getUserActiveSessions.mockResolvedValue(mockSessions);

      const result = await service.getUserActiveSessions('user-123');

      expect(result).toEqual(mockSessions);
      expect(conversationManager.getUserActiveSessions).toHaveBeenCalledWith('user-123');
    });
  });
});
