import { Injectable, Logger, Inject } from '@nestjs/common';
import { EntitiesService } from '../entities/entities.service';
import { ILlmService, CandidateEntity } from '../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
import { EntityListItemResponseDto } from '../entities/dto/entity-list-item-response.dto';
import { ListEntitiesDto } from '../entities/dto/list-entities.dto';
import { VectorSearchDto } from '../entities/dto/vector-search.dto';

@Injectable()
export class RecommendationsService {
  private readonly logger = new Logger(RecommendationsService.name);

  constructor(
    private readonly entitiesService: EntitiesService,
    @Inject('ILlmService') private readonly llmService: ILlmService,
    private readonly llmFactoryService: LlmFactoryService,
  ) {}

  async getRecommendations(
    createRecommendationDto: CreateRecommendationDto,
  ): Promise<RecommendationResponseDto> {
    const { problem_description, filters } = createRecommendationDto;
    const maxCandidates = filters?.max_candidates || 20;

    this.logger.log(
      `Getting recommendations for problem: "${problem_description}" with max ${maxCandidates} candidates`,
    );

    try {
      // Step 1: Use vector search to find semantically relevant entities
      const candidateEntities = await this.findCandidateEntities(
        problem_description,
        filters,
        maxCandidates,
      );

      this.logger.log(`Found ${candidateEntities.length} candidate entities`);

      if (candidateEntities.length === 0) {
        return {
          recommended_entities: [],
          explanation: 'No relevant entities found for your query. Please try a different description or adjust your filters.',
          problem_description,
          candidates_analyzed: 0,
          llm_provider: 'N/A',
          generated_at: new Date(),
        };
      }

      // Step 2: Convert entities to the format expected by LLM service
      const llmCandidates = this.convertToLlmCandidates(candidateEntities);

      // Step 3: Get LLM recommendations
      const llmRecommendation = await this.llmService.getRecommendation(
        problem_description,
        llmCandidates,
      );

      // Step 4: Fetch full entity details for recommended entities
      const recommendedEntities = await this.getRecommendedEntityDetails(
        llmRecommendation.recommendedEntityIds,
        candidateEntities,
      );

      // Step 5: Get current LLM provider for response metadata
      const currentProvider = await this.getCurrentLlmProvider();

      return {
        recommended_entities: recommendedEntities,
        explanation: llmRecommendation.explanation,
        problem_description,
        candidates_analyzed: candidateEntities.length,
        llm_provider: currentProvider,
        generated_at: new Date(),
      };
    } catch (error) {
      this.logger.error('Error generating recommendations', error.stack);
      throw error;
    }
  }

  private async findCandidateEntities(
    problemDescription: string,
    filters: any,
    maxCandidates: number,
  ): Promise<any[]> {
    // Step 1: Use vector search to find semantically relevant entities
    const vectorResults = await this.entitiesService.vectorSearch({
      query: problemDescription,
      limit: maxCandidates * 2, // Get more candidates to allow for filtering
    });

    if (vectorResults.length === 0) {
      this.logger.warn('No vector search results found');
      return [];
    }

    // Step 2: Extract entity IDs from vector search results
    const entityIds = vectorResults.map(result => result.id);

    // Step 3: Apply filters using the regular findAll method
    const listEntitiesDto: ListEntitiesDto = {
      // Don't use searchTerm here to avoid ftsDocument issue
      limit: maxCandidates,
      page: 1,
      status: filters?.status,
      entityTypeIds: filters?.entity_type_ids,
      categoryIds: filters?.category_ids,
      tagIds: filters?.tag_ids,
      featureIds: filters?.feature_ids,
      hasFreeTier: filters?.has_free_tier,
      employeeCountRanges: filters?.employee_count_ranges,
      fundingStages: filters?.funding_stages,
      pricingModels: filters?.pricing_models,
      priceRanges: filters?.price_ranges,
    };

    // Step 4: Get full entity data for the vector search results, applying filters
    const result = await this.entitiesService.findAll(listEntitiesDto);

    // Step 5: Filter the results to only include entities from vector search and maintain order
    const filteredEntities = entityIds
      .map(id => result.data.find(entity => entity.id === id))
      .filter(entity => entity !== undefined)
      .slice(0, maxCandidates);

    return filteredEntities;
  }

  private convertToLlmCandidates(entities: any[]): CandidateEntity[] {
    return entities.map((entity) => ({
      id: entity.id,
      name: entity.name,
      shortDescription: entity.shortDescription,
      description: entity.description,
      entityType: {
        name: entity.entityType.name,
        slug: entity.entityType.slug,
      },
      categories: entity.entityCategories || [],
      tags: entity.entityTags || [],
      features: entity.entityFeatures || [],
      websiteUrl: entity.websiteUrl,
      logoUrl: entity.logoUrl,
      avgRating: entity.avgRating,
      reviewCount: entity.reviewCount,
    }));
  }

  private async getRecommendedEntityDetails(
    recommendedIds: string[],
    candidateEntities: any[],
  ): Promise<EntityListItemResponseDto[]> {
    // Filter candidate entities to only include recommended ones, maintaining order
    const recommendedEntities = recommendedIds
      .map((id) => candidateEntities.find((entity) => entity.id === id))
      .filter((entity) => entity !== undefined);

    // Convert to response DTOs (you may need to implement this mapping)
    return recommendedEntities.map((entity) => this.mapToEntityListItemResponseDto(entity));
  }

  private mapToEntityListItemResponseDto(entity: any): EntityListItemResponseDto {
    // This should match the mapping logic from EntitiesController
    const listItemDto = new EntityListItemResponseDto();
    listItemDto.id = entity.id;
    listItemDto.name = entity.name;
    listItemDto.slug = entity.slug;
    listItemDto.logoUrl = entity.logoUrl;
    listItemDto.shortDescription = entity.shortDescription;
    listItemDto.websiteUrl = entity.websiteUrl;
    listItemDto.entityType = {
      name: entity.entityType.name,
      slug: entity.entityType.slug,
    };
    listItemDto.avgRating = entity.avgRating;
    listItemDto.reviewCount = entity.reviewCount;
    listItemDto.saveCount = entity._count?.userSavedEntities ?? 0;

    if (entity.entityType?.slug === 'ai-tool' && entity.entityDetailsTool) {
      listItemDto.hasFreeTier = entity.entityDetailsTool.hasFreeTier;
    }

    return listItemDto;
  }

  private async getCurrentLlmProvider(): Promise<string> {
    try {
      // Get the current provider from the factory service
      const providers = this.llmFactoryService.getAvailableProviders();
      // For now, we'll determine the provider by checking which service is being used
      // In a more sophisticated implementation, you might want to expose this from the factory
      return 'OPENAI'; // Default assumption, could be enhanced
    } catch (error) {
      this.logger.warn('Could not determine current LLM provider', error.message);
      return 'UNKNOWN';
    }
  }
}
