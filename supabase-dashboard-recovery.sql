-- =====================================================
-- SUPABASE DASHBOARD RECOVERY SCRIPT
-- Run this in your Supabase SQL Editor
-- =====================================================

-- Step 1: Check current database state
SELECT 'Checking database state...' as status;

-- List all tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;

-- Check if core tables exist
SELECT 
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') 
    THEN 'EXISTS' ELSE 'MISSING' END as users_table,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'entities') 
    THEN 'EXISTS' ELSE 'MISSING' END as entities_table,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = '_prisma_migrations') 
    THEN 'EXISTS' ELSE 'MISSING' END as migrations_table;

-- Step 2: If tables are missing, we need to recreate the basic schema
-- This is a minimal schema to get started

-- Create basic enums if they don't exist
DO $$ BEGIN
    CREATE TYPE "public"."UserRole" AS ENUM ('USER', 'ADMIN', 'MODERATOR');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."UserStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."EntityStatus" AS ENUM ('PENDING', 'ACTIVE', 'REJECTED', 'NEEDS_REVISION', 'INACTIVE', 'ARCHIVED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."TechnicalLevel" AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create basic users table if it doesn't exist
CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "auth_user_id" UUID NOT NULL,
    "username" TEXT,
    "display_name" TEXT,
    "email" TEXT NOT NULL,
    "role" "public"."UserRole" NOT NULL DEFAULT 'USER',
    "status" "public"."UserStatus" NOT NULL DEFAULT 'ACTIVE',
    "technical_level" "public"."TechnicalLevel",
    "profile_picture_url" TEXT,
    "bio" TEXT,
    "social_links" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_login" TIMESTAMP(3),
    
    -- New statistics fields
    "bookmarks_count" INTEGER NOT NULL DEFAULT 0,
    "reputation_score" INTEGER NOT NULL DEFAULT 0,
    "requests_fulfilled" INTEGER NOT NULL DEFAULT 0,
    "requests_made" INTEGER NOT NULL DEFAULT 0,
    "reviews_count" INTEGER NOT NULL DEFAULT 0,
    "tools_approved" INTEGER NOT NULL DEFAULT 0,
    "tools_submitted" INTEGER NOT NULL DEFAULT 0,
    
    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- Create basic entity_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS "public"."entity_types" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "description" TEXT,
    "slug" TEXT NOT NULL,
    "icon_url" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "entity_types_pkey" PRIMARY KEY ("id")
);

-- Create basic entities table if it doesn't exist
CREATE TABLE IF NOT EXISTS "public"."entities" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "slug" TEXT,
    "website_url" TEXT,
    "entity_type_id" UUID NOT NULL,
    "short_description" TEXT,
    "description" TEXT,
    "logo_url" TEXT,
    "documentation_url" TEXT,
    "contact_url" TEXT,
    "privacy_policy_url" TEXT,
    "founded_year" INTEGER,
    "status" "public"."EntityStatus" NOT NULL DEFAULT 'PENDING',
    "social_links" JSONB,
    "submitter_id" UUID NOT NULL,
    "legacy_id" TEXT,
    "review_count" INTEGER NOT NULL DEFAULT 0,
    "avg_rating" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "fts" tsvector,
    CONSTRAINT "entities_pkey" PRIMARY KEY ("id")
);

-- Add basic indexes
CREATE UNIQUE INDEX IF NOT EXISTS "users_auth_user_id_key" ON "public"."users"("auth_user_id");
CREATE UNIQUE INDEX IF NOT EXISTS "users_email_key" ON "public"."users"("email");
CREATE UNIQUE INDEX IF NOT EXISTS "entity_types_name_key" ON "public"."entity_types"("name");
CREATE UNIQUE INDEX IF NOT EXISTS "entity_types_slug_key" ON "public"."entity_types"("slug");
CREATE UNIQUE INDEX IF NOT EXISTS "entities_name_key" ON "public"."entities"("name");

-- Add foreign keys
DO $$ BEGIN
    ALTER TABLE "public"."entities" ADD CONSTRAINT "entities_entity_type_id_fkey" 
    FOREIGN KEY ("entity_type_id") REFERENCES "public"."entity_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."entities" ADD CONSTRAINT "entities_submitter_id_fkey" 
    FOREIGN KEY ("submitter_id") REFERENCES "public"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Insert basic entity types if they don't exist
INSERT INTO "public"."entity_types" ("name", "description", "slug") 
VALUES 
    ('AI Tool', 'Artificial Intelligence tools and platforms', 'ai-tool'),
    ('API', 'Application Programming Interfaces', 'api'),
    ('Dataset', 'Data collections and datasets', 'dataset'),
    ('Model', 'Machine learning models', 'model'),
    ('Platform', 'Development and deployment platforms', 'platform')
ON CONFLICT ("name") DO NOTHING;

-- Success message
SELECT 'Basic database schema restored successfully!' as result;
SELECT 'Next: Run the user profile system migration' as next_step;
