-- Safe Migration Script for User Profile System
-- This script adds the new features without breaking existing data

-- Step 1: Create new enums
DO $$ BEGIN
    CREATE TYPE "public"."ProfileVisibility" AS ENUM ('PUBLIC', 'PRIVATE', 'FRIENDS');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."Theme" AS ENUM ('LIGHT', 'DARK', 'SYSTEM');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."DefaultView" AS ENUM ('GRID', 'LIST');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."ToolRequestPriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."ToolRequestStatus" AS ENUM ('PENDING', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'COMPLETED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."SubmissionStatus" AS ENUM ('PENDING', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'PUBLISHED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "public"."ProfileActivityType" AS ENUM ('BOOKMARK', 'REVIEW', 'SUBMISSION', 'REQUEST', 'VOTE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 2: Add new columns to users table (safe - with defaults)
ALTER TABLE "public"."users" 
ADD COLUMN IF NOT EXISTS "bookmarks_count" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS "reputation_score" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS "requests_fulfilled" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS "requests_made" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS "reviews_count" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS "tools_approved" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS "tools_submitted" INTEGER NOT NULL DEFAULT 0;

-- Step 3: Create app_settings table
CREATE TABLE IF NOT EXISTS "public"."app_settings" (
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "app_settings_pkey" PRIMARY KEY ("key")
);

-- Step 4: Create user_preferences table
CREATE TABLE IF NOT EXISTS "public"."user_preferences" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "email_notifications" BOOLEAN NOT NULL DEFAULT true,
    "marketing_emails" BOOLEAN NOT NULL DEFAULT false,
    "weekly_digest" BOOLEAN NOT NULL DEFAULT true,
    "new_tool_alerts" BOOLEAN NOT NULL DEFAULT true,
    "profile_visibility" "public"."ProfileVisibility" NOT NULL DEFAULT 'PUBLIC',
    "show_bookmarks" BOOLEAN NOT NULL DEFAULT true,
    "show_reviews" BOOLEAN NOT NULL DEFAULT true,
    "show_activity" BOOLEAN NOT NULL DEFAULT true,
    "theme" "public"."Theme" NOT NULL DEFAULT 'LIGHT',
    "items_per_page" INTEGER NOT NULL DEFAULT 20,
    "default_view" "public"."DefaultView" NOT NULL DEFAULT 'GRID',
    "preferred_categories" TEXT[],
    "blocked_categories" TEXT[],
    "content_language" TEXT NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "user_preferences_pkey" PRIMARY KEY ("id")
);

-- Step 5: Create tool_requests table
CREATE TABLE IF NOT EXISTS "public"."tool_requests" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "tool_name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "category_suggestion" TEXT,
    "website_url" TEXT,
    "priority" "public"."ToolRequestPriority" NOT NULL DEFAULT 'MEDIUM',
    "status" "public"."ToolRequestStatus" NOT NULL DEFAULT 'PENDING',
    "admin_notes" TEXT,
    "votes" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "tool_requests_pkey" PRIMARY KEY ("id")
);

-- Step 6: Create user_submitted_tools table
CREATE TABLE IF NOT EXISTS "public"."user_submitted_tools" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "entity_id" UUID NOT NULL,
    "submission_status" "public"."SubmissionStatus" NOT NULL DEFAULT 'PENDING',
    "submitted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reviewed_at" TIMESTAMP(3),
    "reviewer_id" UUID,
    "reviewer_notes" TEXT,
    "changes_requested" TEXT,
    CONSTRAINT "user_submitted_tools_pkey" PRIMARY KEY ("id")
);

-- Step 7: Create profile_activities table
CREATE TABLE IF NOT EXISTS "public"."profile_activities" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "type" "public"."ProfileActivityType" NOT NULL,
    "description" TEXT NOT NULL,
    "entity_id" UUID,
    "entity_name" TEXT,
    "entity_slug" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "profile_activities_pkey" PRIMARY KEY ("id")
);

-- Step 8: Add indexes (safe)
CREATE UNIQUE INDEX IF NOT EXISTS "user_preferences_user_id_key" ON "public"."user_preferences"("user_id");
CREATE UNIQUE INDEX IF NOT EXISTS "user_submitted_tools_user_id_entity_id_key" ON "public"."user_submitted_tools"("user_id", "entity_id");

-- Step 9: Add foreign keys (safe)
DO $$ BEGIN
    ALTER TABLE "public"."user_preferences" ADD CONSTRAINT "user_preferences_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."tool_requests" ADD CONSTRAINT "tool_requests_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."user_submitted_tools" ADD CONSTRAINT "user_submitted_tools_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."user_submitted_tools" ADD CONSTRAINT "user_submitted_tools_entity_id_fkey" 
    FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."user_submitted_tools" ADD CONSTRAINT "user_submitted_tools_reviewer_id_fkey" 
    FOREIGN KEY ("reviewer_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."profile_activities" ADD CONSTRAINT "profile_activities_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE "public"."profile_activities" ADD CONSTRAINT "profile_activities_entity_id_fkey" 
    FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE SET NULL ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Step 10: Handle slug column safely (only if entities table exists)
DO $$ 
BEGIN
    -- First, add slug column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'entities' AND column_name = 'slug') THEN
        ALTER TABLE "public"."entities" ADD COLUMN "slug" TEXT;
    END IF;
    
    -- Update any NULL slugs with generated values
    UPDATE "public"."entities" 
    SET "slug" = LOWER(REGEXP_REPLACE("name", '[^a-zA-Z0-9]+', '-', 'g'))
    WHERE "slug" IS NULL AND "name" IS NOT NULL;
    
    -- Handle any remaining NULLs
    UPDATE "public"."entities" 
    SET "slug" = 'entity-' || "id"
    WHERE "slug" IS NULL;
    
    -- Now make it NOT NULL
    ALTER TABLE "public"."entities" ALTER COLUMN "slug" SET NOT NULL;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not update entities table: %', SQLERRM;
END $$;

-- Success message
SELECT 'User profile system migration completed successfully!' as result;
