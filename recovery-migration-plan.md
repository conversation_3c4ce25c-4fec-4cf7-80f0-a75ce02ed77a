# Database Recovery Migration Plan

## Problem Analysis
The migration failed because:
1. `entities.slug` was made required but existing entities had NULL slugs
2. This caused a cascade failure that wiped the database

## Safe Recovery Steps

### Step 1: Prepare Schema for Safe Migration
Before running migrations, we need to modify the schema to handle existing data:

```prisma
// In schema.prisma, temporarily make slug optional
model Entity {
  // ... other fields
  slug String? // Make optional first
  // ... rest of model
}
```

### Step 2: Create Data-Safe Migration Sequence

1. **Migration 1**: Add new tables without constraints
2. **Migration 2**: Populate missing slugs for existing entities
3. **Migration 3**: Make slug required after population
4. **Migration 4**: Add triggers and indexes

### Step 3: Manual Slug Population Script

```sql
-- Update entities with missing slugs
UPDATE entities 
SET slug = LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]+', '-', 'g'))
WHERE slug IS NULL;

-- Ensure uniqueness
WITH numbered_entities AS (
  SELECT id, slug, 
         ROW_NUMBER() OVER (PARTITION BY slug ORDER BY created_at) as rn
  FROM entities
)
UPDATE entities 
SET slug = entities.slug || '-' || (numbered_entities.rn - 1)
FROM numbered_entities
WHERE entities.id = numbered_entities.id 
  AND numbered_entities.rn > 1;
```

### Step 4: Rollback Current Migrations

```bash
# If database is accessible, rollback problematic migrations
npx prisma migrate resolve --rolled-back 20250620230620_add_user_profile_system
npx prisma migrate resolve --rolled-back 20250620230645_add_user_stats_triggers
npx prisma migrate resolve --rolled-back 20250620230816_
npx prisma migrate resolve --rolled-back 20250620_remove_save_count_trigger
```

### Step 5: Recreate Migrations Safely

1. First, ensure all entities have slugs
2. Then run the profile system migration
3. Add triggers last

## Prevention for Future

1. **Always backup before major migrations**
2. **Test migrations on staging first**
3. **Use data migration scripts for schema changes affecting existing data**
4. **Make breaking changes in multiple steps**

## Immediate Actions Needed

1. Check if you have a Supabase backup available
2. If yes, restore from backup
3. If no, we'll need to rebuild with the safe migration plan above
