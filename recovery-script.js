const { PrismaClient } = require('./generated/prisma');

async function recoverDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking database connection...');
    
    // Test connection
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Check what tables exist
    console.log('🔍 Checking existing tables...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    
    console.log('📋 Existing tables:', tables.map(t => t.table_name));
    
    // Check if users table exists and has data
    try {
      const userCount = await prisma.user.count();
      console.log(`👥 Users table exists with ${userCount} users`);
    } catch (error) {
      console.log('❌ Users table issue:', error.message);
    }
    
    // Check if entities table exists and has data
    try {
      const entityCount = await prisma.entity.count();
      console.log(`🔧 Entities table exists with ${entityCount} entities`);
    } catch (error) {
      console.log('❌ Entities table issue:', error.message);
    }
    
    // Check migration status
    console.log('🔍 Checking migration status...');
    try {
      const migrations = await prisma.$queryRaw`
        SELECT migration_name, finished_at, rolled_back_at 
        FROM _prisma_migrations 
        ORDER BY finished_at DESC 
        LIMIT 10;
      `;
      console.log('📝 Recent migrations:', migrations);
    } catch (error) {
      console.log('❌ Migration table issue:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Database recovery check failed:', error.message);
    
    if (error.code === 'P1001') {
      console.log('🔧 Connection issue detected. Please check:');
      console.log('1. Your internet connection');
      console.log('2. Supabase project is running');
      console.log('3. Database credentials in .env are correct');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the recovery check
recoverDatabase()
  .then(() => {
    console.log('🎉 Recovery check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Recovery check failed:', error);
    process.exit(1);
  });
