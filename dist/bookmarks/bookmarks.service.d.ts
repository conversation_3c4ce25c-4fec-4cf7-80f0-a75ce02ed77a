import { PrismaService } from '../prisma/prisma.service';
import { Entity, UserSavedEntity } from 'generated/prisma';
import { ListBookmarksDto } from './dto/list-bookmarks.dto';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { AppLoggerService } from '../common/logger/logger.service';
export declare class BookmarksService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService, logger: AppLoggerService);
    saveBookmark(userId: string, createBookmarkDto: CreateBookmarkDto): Promise<UserSavedEntity>;
    unsaveBookmark(userId: string, entityId: string): Promise<void>;
    listSavedBookmarks(userId: string, listBookmarksDto: ListBookmarksDto): Promise<{
        data: Entity[];
        total: number;
        page: number;
        limit: number;
    }>;
}
