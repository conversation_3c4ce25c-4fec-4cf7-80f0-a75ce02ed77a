import { PrismaService } from '../prisma/prisma.service';
import { Entity, UserSavedEntity } from 'generated/prisma';
import { ListBookmarksDto } from './dto/list-bookmarks.dto';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { AppLoggerService } from '../common/logger/logger.service';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ValidationService } from '../common/validation.service';
export declare class BookmarksService {
    private readonly prisma;
    private readonly logger;
    private readonly activityLogger;
    private readonly validationService;
    constructor(prisma: PrismaService, logger: AppLoggerService, activityLogger: ActivityLoggerService, validationService: ValidationService);
    saveBookmark(userId: string, createBookmarkDto: CreateBookmarkDto): Promise<UserSavedEntity>;
    unsaveBookmark(userId: string, entityId: string): Promise<void>;
    listSavedBookmarks(userId: string, listBookmarksDto: ListBookmarksDto): Promise<{
        data: Entity[];
        total: number;
        page: number;
        limit: number;
    }>;
}
