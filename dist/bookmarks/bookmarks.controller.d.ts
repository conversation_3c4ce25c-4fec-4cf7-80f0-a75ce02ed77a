import { BookmarksService } from './bookmarks.service';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { ListBookmarksDto } from './dto/list-bookmarks.dto';
import { User as UserModel } from 'generated/prisma';
import { AppLoggerService } from '../common/logger/logger.service';
export declare class BookmarksController {
    private readonly bookmarksService;
    private readonly logger;
    constructor(bookmarksService: BookmarksService, logger: AppLoggerService);
    saveBookmark(user: UserModel, createBookmarkDto: CreateBookmarkDto): Promise<{
        id: string;
        entityId: string;
        userId: string;
        createdAt: Date;
    }>;
    unsaveBookmark(user: UserModel, entityId: string): Promise<void>;
    listSavedBookmarks(user: UserModel, listBookmarksDto: ListBookmarksDto): Promise<{
        data: import("generated/prisma").Entity[];
        total: number;
        page: number;
        limit: number;
    }>;
}
