"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookmarksController = void 0;
const common_1 = require("@nestjs/common");
const bookmarks_service_1 = require("./bookmarks.service");
const create_bookmark_dto_1 = require("./dto/create-bookmark.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const list_bookmarks_dto_1 = require("./dto/list-bookmarks.dto");
const logger_service_1 = require("../common/logger/logger.service");
let BookmarksController = class BookmarksController {
    constructor(bookmarksService, logger) {
        this.bookmarksService = bookmarksService;
        this.logger = logger;
    }
    async saveBookmark(user, createBookmarkDto) {
        if (!user?.id) {
            this.logger.logError(new Error('User ID missing in bookmark request'), {
                operation: 'saveBookmark',
                entityId: createBookmarkDto.entity_id,
                type: 'missing_user_id',
            });
            throw new common_1.BadRequestException('User authentication failed. Please log in again.');
        }
        this.logger.log('Bookmark save request received', {
            operation: 'saveBookmark',
            userId: user.id,
            entityId: createBookmarkDto.entity_id,
        });
        return this.bookmarksService.saveBookmark(user.id, createBookmarkDto);
    }
    async unsaveBookmark(user, entityId) {
        if (!user?.id) {
            this.logger.logError(new Error('User ID missing in bookmark delete request'), {
                operation: 'unsaveBookmark',
                entityId,
                type: 'missing_user_id',
            });
            throw new common_1.BadRequestException('User authentication failed. Please log in again.');
        }
        this.logger.log('Bookmark delete request received', {
            operation: 'unsaveBookmark',
            userId: user.id,
            entityId,
        });
        return this.bookmarksService.unsaveBookmark(user.id, entityId);
    }
    async listSavedBookmarks(user, listBookmarksDto) {
        if (!user?.id) {
            this.logger.logError(new Error('User ID missing in bookmark list request'), {
                operation: 'listSavedBookmarks',
                type: 'missing_user_id',
            });
            throw new common_1.BadRequestException('User authentication failed. Please log in again.');
        }
        this.logger.log('Bookmark list request received', {
            operation: 'listSavedBookmarks',
            userId: user.id,
            page: listBookmarksDto.page,
            limit: listBookmarksDto.limit,
        });
        return this.bookmarksService.listSavedBookmarks(user.id, listBookmarksDto);
    }
};
exports.BookmarksController = BookmarksController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Save (bookmark) an entity' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Entity bookmarked successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Entity not found.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_bookmark_dto_1.CreateBookmarkDto]),
    __metadata("design:returntype", Promise)
], BookmarksController.prototype, "saveBookmark", null);
__decorate([
    (0, common_1.Delete)(':entity_id'),
    (0, swagger_1.ApiOperation)({ summary: 'Unsave (unbookmark) an entity' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Entity unbookmarked successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Bookmark or Entity not found.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('entity_id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BookmarksController.prototype, "unsaveBookmark", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all entities bookmarked by the current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of bookmarked entities.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, list_bookmarks_dto_1.ListBookmarksDto]),
    __metadata("design:returntype", Promise)
], BookmarksController.prototype, "listSavedBookmarks", null);
exports.BookmarksController = BookmarksController = __decorate([
    (0, swagger_1.ApiTags)('Bookmarks'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('bookmarks'),
    __metadata("design:paramtypes", [bookmarks_service_1.BookmarksService,
        logger_service_1.AppLoggerService])
], BookmarksController);
//# sourceMappingURL=bookmarks.controller.js.map