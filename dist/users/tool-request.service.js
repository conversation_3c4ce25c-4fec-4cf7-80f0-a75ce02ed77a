"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRequestService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const activity_logger_service_1 = require("../common/activity-logger.service");
const validation_service_1 = require("../common/validation.service");
let ToolRequestService = class ToolRequestService {
    constructor(prismaService, activityLogger, validationService) {
        this.prismaService = prismaService;
        this.activityLogger = activityLogger;
        this.validationService = validationService;
    }
    async createToolRequest(userId, createDto) {
        await this.validationService.validateToolRequest(userId, createDto.toolName);
        const toolRequest = await this.prismaService.toolRequest.create({
            data: {
                userId,
                toolName: createDto.toolName,
                description: createDto.description,
                reason: createDto.reason,
                categorySuggestion: createDto.categorySuggestion,
                websiteUrl: createDto.websiteUrl,
                priority: createDto.priority || 'MEDIUM',
            },
        });
        await this.activityLogger.logRequestActivity(userId, createDto.toolName, 'created');
        return this.mapToResponseDto(toolRequest);
    }
    async getUserToolRequests(userId, listDto) {
        const { page = 1, limit = 20, status, priority } = listDto;
        const skip = (page - 1) * limit;
        const where = { userId };
        if (status)
            where.status = status;
        if (priority)
            where.priority = priority;
        const [toolRequests, total] = await Promise.all([
            this.prismaService.toolRequest.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
            }),
            this.prismaService.toolRequest.count({ where }),
        ]);
        return {
            data: toolRequests.map(this.mapToResponseDto),
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getAllToolRequests(listDto) {
        const { page = 1, limit = 20, status, priority } = listDto;
        const skip = (page - 1) * limit;
        const where = {};
        if (status)
            where.status = status;
        if (priority)
            where.priority = priority;
        const [toolRequests, total] = await Promise.all([
            this.prismaService.toolRequest.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            displayName: true,
                        },
                    },
                },
            }),
            this.prismaService.toolRequest.count({ where }),
        ]);
        return {
            data: toolRequests.map(this.mapToResponseDto),
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    mapToResponseDto(toolRequest) {
        return {
            id: toolRequest.id,
            userId: toolRequest.userId,
            toolName: toolRequest.toolName,
            description: toolRequest.description,
            reason: toolRequest.reason,
            categorySuggestion: toolRequest.categorySuggestion || undefined,
            websiteUrl: toolRequest.websiteUrl || undefined,
            priority: toolRequest.priority,
            status: toolRequest.status,
            adminNotes: toolRequest.adminNotes || undefined,
            votes: toolRequest.votes,
            createdAt: toolRequest.createdAt,
            updatedAt: toolRequest.updatedAt,
        };
    }
};
exports.ToolRequestService = ToolRequestService;
exports.ToolRequestService = ToolRequestService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        activity_logger_service_1.ActivityLoggerService,
        validation_service_1.ValidationService])
], ToolRequestService);
//# sourceMappingURL=tool-request.service.js.map