"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersModule = void 0;
const common_1 = require("@nestjs/common");
const users_controller_1 = require("./users.controller");
const tool_requests_controller_1 = require("./tool-requests.controller");
const users_service_1 = require("./users.service");
const user_preferences_service_1 = require("./user-preferences.service");
const tool_request_service_1 = require("./tool-request.service");
const user_submitted_tools_service_1 = require("./user-submitted-tools.service");
const comprehensive_profile_service_1 = require("./comprehensive-profile.service");
const prisma_module_1 = require("../prisma/prisma.module");
const supabase_module_1 = require("../supabase/supabase.module");
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            supabase_module_1.SupabaseModule,
        ],
        controllers: [users_controller_1.UserController, tool_requests_controller_1.ToolRequestsController],
        providers: [users_service_1.UserService, user_preferences_service_1.UserPreferencesService, tool_request_service_1.ToolRequestService, user_submitted_tools_service_1.UserSubmittedToolsService, comprehensive_profile_service_1.ComprehensiveProfileService],
        exports: [users_service_1.UserService, user_preferences_service_1.UserPreferencesService, tool_request_service_1.ToolRequestService, user_submitted_tools_service_1.UserSubmittedToolsService, comprehensive_profile_service_1.ComprehensiveProfileService]
    })
], UsersModule);
//# sourceMappingURL=users.module.js.map