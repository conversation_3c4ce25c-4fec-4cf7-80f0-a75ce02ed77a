export interface LlmRecommendation {
    recommendedEntityIds: string[];
    explanation: string;
}
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface UserIntent {
    type: 'discovery' | 'comparison' | 'specific_tool' | 'general_question' | 'refinement';
    confidence: number;
    entities?: string[];
    categories?: string[];
    features?: string[];
    constraints?: {
        budget?: 'free' | 'low' | 'medium' | 'high';
        technical_level?: 'beginner' | 'intermediate' | 'advanced';
        use_case?: string;
    };
}
export interface ConversationContext {
    sessionId: string;
    userId: string;
    messages: ChatMessage[];
    currentIntent?: UserIntent;
    discoveredEntities: string[];
    userPreferences: {
        budget?: 'free' | 'low' | 'medium' | 'high';
        technical_level?: 'beginner' | 'intermediate' | 'advanced';
        preferred_categories?: string[];
        excluded_categories?: string[];
    };
    conversationStage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
    metadata: {
        startedAt: Date;
        lastActiveAt: Date;
        totalMessages: number;
        entitiesShown: string[];
    };
}
export interface ChatResponse {
    message: string;
    intent: UserIntent;
    suggestedActions?: Array<{
        type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';
        label: string;
        data?: any;
    }>;
    discoveredEntities?: Array<{
        id: string;
        name: string;
        relevanceScore: number;
        reason: string;
    }>;
    followUpQuestions?: string[];
    shouldTransitionToRecommendations: boolean;
    conversationStage: ConversationContext['conversationStage'];
    metadata: {
        responseTime: number;
        llmProvider: string;
        tokensUsed?: number;
    };
}
export interface CandidateEntity {
    id: string;
    name: string;
    shortDescription: string | null;
    description: string | null;
    entityType: {
        name: string;
        slug: string;
    };
    categories: Array<{
        category: {
            name: string;
            slug: string;
        };
    }>;
    tags: Array<{
        tag: {
            name: string;
            slug: string;
        };
    }>;
    features: Array<{
        feature: {
            name: string;
            slug: string;
        };
    }>;
    websiteUrl?: string | null;
    logoUrl?: string | null;
    avgRating?: number;
    reviewCount?: number;
}
export interface ILlmService {
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    getChatResponse(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>;
    classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent>;
    generateFollowUpQuestions(context: ConversationContext): Promise<string[]>;
    shouldTransitionToRecommendations(context: ConversationContext): Promise<{
        shouldTransition: boolean;
        reason: string;
    }>;
}
