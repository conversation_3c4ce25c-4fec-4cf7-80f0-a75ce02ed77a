"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendChatMessageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const user_preferences_dto_1 = require("./user-preferences.dto");
class SendChatMessageDto {
}
exports.SendChatMessageDto = SendChatMessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The user message to send to the chatbot',
        example: 'I need help finding an AI tool for automated code documentation',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendChatMessageDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional session ID to continue an existing conversation. If not provided, a new session will be created.',
        example: 'chat_123e4567-e89b-12d3-a456-426614174000',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendChatMessageDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User preferences to help personalize the conversation',
        type: user_preferences_dto_1.UserPreferencesDto,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => user_preferences_dto_1.UserPreferencesDto),
    __metadata("design:type", user_preferences_dto_1.UserPreferencesDto)
], SendChatMessageDto.prototype, "user_preferences", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional context or metadata for the message',
        example: { source: 'web', page: 'dashboard' },
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SendChatMessageDto.prototype, "context", void 0);
//# sourceMappingURL=send-chat-message.dto.js.map