"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiscoveredEntityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class DiscoveredEntityDto {
}
exports.DiscoveredEntityDto = DiscoveredEntityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier of the discovered entity',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(4),
    __metadata("design:type", String)
], DiscoveredEntityDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the discovered entity',
        example: 'GitHub Copilot',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DiscoveredEntityDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Relevance score for this entity (0.0 to 1.0)',
        example: 0.95,
        minimum: 0,
        maximum: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], DiscoveredEntityDto.prototype, "relevance_score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Explanation of why this entity is relevant',
        example: 'This tool is perfect for automated code documentation as it integrates directly with your IDE and understands your codebase context.',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DiscoveredEntityDto.prototype, "reason", void 0);
//# sourceMappingURL=discovered-entity.dto.js.map