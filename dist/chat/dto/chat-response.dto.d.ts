import { SuggestedActionDto } from './suggested-action.dto';
import { DiscoveredEntityDto } from './discovered-entity.dto';
export declare class ChatResponseMetadataDto {
    response_time: number;
    llm_provider: string;
    tokens_used?: number;
}
export declare class ChatResponseDto {
    message: string;
    session_id: string;
    conversation_stage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
    suggested_actions?: SuggestedActionDto[];
    discovered_entities?: DiscoveredEntityDto[];
    follow_up_questions?: string[];
    should_transition_to_recommendations: boolean;
    metadata: ChatResponseMetadataDto;
    generated_at: Date;
}
