"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatResponseDto = exports.ChatResponseMetadataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const suggested_action_dto_1 = require("./suggested-action.dto");
const discovered_entity_dto_1 = require("./discovered-entity.dto");
class ChatResponseMetadataDto {
}
exports.ChatResponseMetadataDto = ChatResponseMetadataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Response time in milliseconds',
        example: 1250,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], ChatResponseMetadataDto.prototype, "response_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'LLM provider used for this response',
        example: 'OPENAI',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ChatResponseMetadataDto.prototype, "llm_provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of tokens used (if available)',
        example: 150,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ChatResponseMetadataDto.prototype, "tokens_used", void 0);
class ChatResponseDto {
}
exports.ChatResponseDto = ChatResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The chatbot response message',
        example: 'I\'d be happy to help you find the perfect AI tool for code documentation! Based on what you\'ve mentioned, there are several excellent options that could work well for your needs.',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ChatResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Session ID for this conversation',
        example: 'chat_123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ChatResponseDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current stage of the conversation',
        enum: ['greeting', 'discovery', 'refinement', 'recommendation', 'comparison'],
        example: 'discovery',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(['greeting', 'discovery', 'refinement', 'recommendation', 'comparison']),
    __metadata("design:type", String)
], ChatResponseDto.prototype, "conversation_stage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Suggested actions the user can take',
        type: [suggested_action_dto_1.SuggestedActionDto],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => suggested_action_dto_1.SuggestedActionDto),
    __metadata("design:type", Array)
], ChatResponseDto.prototype, "suggested_actions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'AI tools discovered during this conversation turn',
        type: [discovered_entity_dto_1.DiscoveredEntityDto],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => discovered_entity_dto_1.DiscoveredEntityDto),
    __metadata("design:type", Array)
], ChatResponseDto.prototype, "discovered_entities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Follow-up questions to guide the conversation',
        example: ['What programming languages do you primarily work with?', 'Do you prefer free tools or are you open to paid options?'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ChatResponseDto.prototype, "follow_up_questions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the conversation is ready to transition to formal recommendations',
        example: false,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ChatResponseDto.prototype, "should_transition_to_recommendations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Response metadata including timing and provider information',
        type: ChatResponseMetadataDto,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ChatResponseMetadataDto),
    __metadata("design:type", ChatResponseMetadataDto)
], ChatResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the response was generated',
        example: '2024-06-20T01:00:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], ChatResponseDto.prototype, "generated_at", void 0);
//# sourceMappingURL=chat-response.dto.js.map