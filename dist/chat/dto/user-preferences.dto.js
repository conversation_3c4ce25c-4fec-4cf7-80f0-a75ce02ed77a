"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPreferencesDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UserPreferencesDto {
}
exports.UserPreferencesDto = UserPreferencesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User budget preference for AI tools',
        enum: ['free', 'low', 'medium', 'high'],
        example: 'medium',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['free', 'low', 'medium', 'high']),
    __metadata("design:type", String)
], UserPreferencesDto.prototype, "budget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User technical skill level',
        enum: ['beginner', 'intermediate', 'advanced'],
        example: 'intermediate',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['beginner', 'intermediate', 'advanced']),
    __metadata("design:type", String)
], UserPreferencesDto.prototype, "technical_level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Preferred AI tool categories',
        example: ['Developer Tools', 'Content Creation'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UserPreferencesDto.prototype, "preferred_categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Categories to exclude from recommendations',
        example: ['Gaming', 'Entertainment'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UserPreferencesDto.prototype, "excluded_categories", void 0);
//# sourceMappingURL=user-preferences.dto.js.map