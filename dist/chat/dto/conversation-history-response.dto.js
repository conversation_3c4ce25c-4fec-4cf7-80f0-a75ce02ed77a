"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationHistoryResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const chat_message_dto_1 = require("./chat-message.dto");
class ConversationHistoryResponseDto {
}
exports.ConversationHistoryResponseDto = ConversationHistoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Session ID for this conversation',
        example: 'chat_123e4567-e89b-12d3-a456-426614174000',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConversationHistoryResponseDto.prototype, "session_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current stage of the conversation',
        enum: ['greeting', 'discovery', 'refinement', 'recommendation', 'comparison'],
        example: 'discovery',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(['greeting', 'discovery', 'refinement', 'recommendation', 'comparison']),
    __metadata("design:type", String)
], ConversationHistoryResponseDto.prototype, "conversation_stage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of messages in the conversation',
        type: [chat_message_dto_1.ChatMessageDto],
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => chat_message_dto_1.ChatMessageDto),
    __metadata("design:type", Array)
], ConversationHistoryResponseDto.prototype, "messages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of messages in the conversation',
        example: 8,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], ConversationHistoryResponseDto.prototype, "total_messages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of AI tools discovered in this conversation',
        example: 5,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], ConversationHistoryResponseDto.prototype, "discovered_entities_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the conversation was started',
        example: '2024-06-20T01:00:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], ConversationHistoryResponseDto.prototype, "started_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the conversation was last active',
        example: '2024-06-20T01:15:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], ConversationHistoryResponseDto.prototype, "last_active_at", void 0);
//# sourceMappingURL=conversation-history-response.dto.js.map