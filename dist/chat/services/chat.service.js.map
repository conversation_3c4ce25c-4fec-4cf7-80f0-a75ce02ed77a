{"version": 3, "file": "chat.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/chat.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAA4D;AAC5D,iFAA4E;AAC5E,6EAAuE;AACvE,iEAA4D;AAC5D,yFAAmF;AACnF,6DAAwD;AACxD,sEAAkE;AAElE,uFAAkF;AAU3E,IAAM,WAAW,mBAAjB,MAAM,WAAW;IAGtB,YACmB,mBAA+C,EAC/C,gBAAyC,EACzC,WAA+B,EAC/B,kBAAiD,EACjD,YAA8B,EAC9B,eAAgC,EAC1B,UAAwC,EAC9C,iBAAoC;QAPpC,wBAAmB,GAAnB,mBAAmB,CAA4B;QAC/C,qBAAgB,GAAhB,gBAAgB,CAAyB;QACzC,gBAAW,GAAX,WAAW,CAAoB;QAC/B,uBAAkB,GAAlB,kBAAkB,CAA+B;QACjD,iBAAY,GAAZ,YAAY,CAAkB;QAC9B,oBAAe,GAAf,eAAe,CAAiB;QACT,eAAU,GAAV,UAAU,CAAa;QAC9C,sBAAiB,GAAjB,iBAAiB,CAAmB;QAVtC,WAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAWpD,CAAC;IAKJ,KAAK,CAAC,WAAW,CACf,MAAc,EACd,kBAAsC;QAEtC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAC1D,kBAAkB,CAAC,UAAU,IAAI,KAAK,CACvC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,oCAAoC,MAAM,cAAc,kBAAkB,CAAC,UAAU,IAAI,KAAK,cAAc,SAAS,EAAE,CACxH,CAAC;QAEF,IAAI,CAAC;YAEH,IAAI,OAAO,CAAC;YACZ,IAAI,CAAC;gBACH,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAC9D,MAAM,EACN,kBAAkB,CAAC,UAAU,CAC9B,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5E,OAAO,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CACvD,KAAK,EACL,kBAAkB,CAAC,UAAU,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,EACrD,MAAM,CACP,CAAC;YACJ,CAAC;YAGD,IAAI,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;gBACxC,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAClD,OAAO,CAAC,SAAS,EACjB,kBAAkB,CAAC,gBAAgB,CACpC,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAErE,CAAC;YACH,CAAC;YAGD,IAAI,cAAc,CAAC;YACnB,IAAI,CAAC;gBACH,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CACxD,OAAO,CAAC,SAAS,EACjB;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,kBAAkB,CAAC,OAAO;oBACnC,QAAQ,EAAE,kBAAkB,CAAC,OAAO;iBACrC,CACF,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC7E,cAAc,GAAG,OAAO,CAAC;YAC3B,CAAC;YAGD,IAAI,iBAAiB,CAAC;YACtB,IAAI,CAAC;gBACH,iBAAiB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CACrD,kBAAkB,CAAC,OAAO,EAC1B,cAAc,CACf,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACtF,iBAAiB,GAAG,EAAE,CAAC;YACzB,CAAC;YAGD,IAAI,YAAY,CAAC;YACjB,IAAI,CAAC;gBACH,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAC/D,kBAAkB,CAAC,OAAO,EAC1B,cAAc,EACd,iBAAiB,CAClB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC3D,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CACzC,KAAK,EACL,cAAc,EACd,kBAAkB,CAAC,OAAO,CAC3B,CAAC;YACJ,CAAC;YAGD,IAAI,YAAY,GAAG,cAAc,CAAC;YAClC,IAAI,CAAC;gBACH,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CACtD,OAAO,CAAC,SAAS,EACjB;oBACE,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,YAAY,CAAC,OAAO;oBAC7B,QAAQ,EAAE;wBACR,MAAM,EAAE,YAAY,CAAC,MAAM;wBAC3B,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;wBAC9C,YAAY,EAAE,YAAY,CAAC,QAAQ,CAAC,YAAY;qBACjD;iBACF,EACD,YAAY,CAAC,MAAM,CACpB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iDAAiD,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAEnF,CAAC;YAGD,IAAI,YAAY,CAAC,kBAAkB,IAAI,YAAY,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClF,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACjE,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACrF,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAExE,CAAC;YACH,CAAC;YAGD,IAAI,YAAY,CAAC,iBAAiB,KAAK,YAAY,CAAC,iBAAiB,EAAE,CAAC;gBACtE,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CACpD,OAAO,CAAC,SAAS,EACjB,YAAY,CAAC,iBAAiB,CAC/B,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAEvE,CAAC;YACH,CAAC;YAGD,MAAM,QAAQ,GAAoB;gBAChC,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,kBAAkB,EAAE,YAAY,CAAC,iBAAiB;gBAClD,iBAAiB,EAAE,YAAY,CAAC,gBAAgB,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAC/D,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB,CAAC,CAAC;gBACH,mBAAmB,EAAE,YAAY,CAAC,kBAAkB,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACnE,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,eAAe,EAAE,MAAM,CAAC,cAAc;oBACtC,MAAM,EAAE,MAAM,CAAC,MAAM;iBACtB,CAAC,CAAC;gBACH,mBAAmB,EAAE,YAAY,CAAC,iBAAiB;gBACnD,oCAAoC,EAAE,YAAY,CAAC,iCAAiC;gBACpF,QAAQ,EAAE;oBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBACrC,YAAY,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;oBAC/C,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU;iBAC9C;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAGF,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAC3C,SAAS,EACT,QAAQ,CAAC,QAAQ,CAAC,aAAa,EAC/B,IAAI,EACJ,QAAQ,CAAC,QAAQ,CAAC,YAAY,EAC9B,QAAQ,CAAC,kBAAkB,CAC5B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,uCAAuC,OAAO,CAAC,SAAS,OAAO,QAAQ,CAAC,QAAQ,CAAC,aAAa,IAAI,CACnG,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAGzE,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAC3C,SAAS,EACT,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,KAAK,EACL,UAAU,EACV,OAAO,EACP,KAAK,CAAC,OAAO,CACd,CAAC;YAGF,OAAO,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CACtD,kBAAkB,CAAC,UAAU,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE,EACrD,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,MAAc,EACd,SAAiB,EACjB,aAAwC;QAExC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAC;QAEzE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAG1F,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC9B,IAAI,CAAC,gBAAgB,CAAC,eAAe,CACnC,IAAI,KAAK,CAAC,qCAAqC,CAAC,EAChD,SAAS,CACV,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CACpE,SAAS,EACT,aAAa,CAAC,KAAK,CACpB,CAAC;YAEF,MAAM,QAAQ,GAAmC;gBAC/C,UAAU,EAAE,SAAS;gBACrB,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;gBAC7C,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,EAAE,EAAE,GAAG,CAAC,EAAE;oBACV,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,SAAS,EAAE,GAAG,CAAC,SAAS;oBACxB,QAAQ,EAAE,GAAG,CAAC,QAAQ;iBACvB,CAAC,CAAC;gBACH,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,aAAa;gBAC9C,yBAAyB,EAAE,OAAO,CAAC,kBAAkB,CAAC,MAAM;gBAC5D,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,SAAS;gBACtC,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY;aAC9C,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,SAAiB;QACrD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,SAAS,aAAa,MAAM,EAAE,CAAC,CAAC;QAG/E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC1F,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,OAAO,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAChE,CAAC;IAKO,KAAK,CAAC,wBAAwB,CACpC,WAAmB,EACnB,OAAY;QAEZ,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAChE,WAAW,EACX,KAAK,IAAI,EAAE;gBACT,OAAO,OAAO,CAAC,IAAI,CAAC;oBAClB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;wBAChC,KAAK,EAAE,WAAW;wBAClB,KAAK,EAAE,EAAE;qBACV,CAAC;oBACF,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,EAAE,KAAK,CAAC,CACpE;iBACF,CAAC,CAAC;YACL,CAAC,CACF,CAAC;YAEF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;gBAC7D,OAAO,EAAE,CAAC;YACZ,CAAC;YAGD,IAAI,eAAe,GAAG,aAAa,CAAC;YAEpC,IAAI,CAAC;gBACH,IAAI,OAAO,CAAC,eAAe,EAAE,mBAAmB,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7D,eAAe,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;wBAC9C,IAAI,CAAC;4BACH,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BACnF,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE,CAC5E,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CACpC,CAAC;wBACJ,CAAC;wBAAC,OAAO,WAAW,EAAE,CAAC;4BACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+CAA+C,EAAE,WAAW,CAAC,CAAC;4BAC/E,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,OAAO,CAAC,eAAe,EAAE,oBAAoB,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;oBAE9D,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC9C,IAAI,CAAC;4BACH,MAAM,gBAAgB,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CACrD,OAAO,CAAC,eAAe,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CACvE,IAAI,KAAK,CAAC;4BACX,MAAM,gBAAgB,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CACrD,OAAO,CAAC,eAAe,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CACvE,IAAI,KAAK,CAAC;4BAEX,IAAI,gBAAgB,IAAI,CAAC,gBAAgB;gCAAE,OAAO,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,gBAAgB,IAAI,gBAAgB;gCAAE,OAAO,CAAC,CAAC;4BACpD,OAAO,CAAC,CAAC;wBACX,CAAC;wBAAC,OAAO,SAAS,EAAE,CAAC;4BACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,SAAS,CAAC,CAAC;4BACrE,OAAO,CAAC,CAAC;wBACX,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qDAAqD,EAAE,gBAAgB,CAAC,CAAC;YAE5F,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,SAAS,eAAe,CAAC,MAAM,qCAAqC,CACrE,CAAC;YAEF,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAGtE,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjF,MAAM,KAAK,CAAC;YACd,CAAC;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAKD,qBAAqB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;IAC9C,CAAC;IAKD,oBAAoB;QAClB,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;IACnD,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;IAC3C,CAAC;IAKD,WAAW;QACT,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;IACrC,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YAGH,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;YACjE,OAAO,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrE,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;CACF,CAAA;AA7ZY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAWR,WAAA,IAAA,eAAM,EAAC,aAAa,CAAC,CAAA;qCANgB,yDAA0B;QAC7B,oDAAuB;QAC5B,yCAAkB;QACX,gEAA6B;QACnC,qCAAgB;QACb,kCAAe,UAEb,uCAAiB;GAX5C,WAAW,CA6ZvB"}