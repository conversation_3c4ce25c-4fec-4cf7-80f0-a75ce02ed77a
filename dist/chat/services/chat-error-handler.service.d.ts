import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';
export declare class ChatErrorHandlerService {
    private readonly logger;
    handleLlmError(error: any, context: ConversationContext, userMessage: string): ChatResponseDto;
    handleConversationStateError(error: any, sessionId: string, userId: string): ChatResponseDto;
    handleEntityDiscoveryError(error: any, context: ConversationContext, userMessage: string): ChatResponseDto;
    handleAuthError(error: any, sessionId?: string): never;
    handleValidationError(error: any, sessionId?: string): never;
    createCriticalErrorFallback(sessionId: string, error: any): ChatResponseDto;
    private isRateLimitError;
    private isApiKeyError;
    private isNetworkError;
    private isQuotaExceededError;
    private createRateLimitFallbackResponse;
    private createApiKeyFallbackResponse;
    private createNetworkFallbackResponse;
    private createQuotaFallbackResponse;
    private createGenericLlmFallbackResponse;
}
