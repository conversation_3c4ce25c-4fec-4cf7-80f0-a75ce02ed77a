import { ConfigService } from '@nestjs/config';
export declare class ChatPerformanceMonitorService {
    private readonly configService;
    private readonly logger;
    private metrics;
    private readonly thresholds;
    constructor(configService: ConfigService);
    recordRequestStart(sessionId: string): string;
    recordRequestComplete(requestId: string, responseTimeMs: number, success: boolean, llmProvider: string, conversationStage: string, error?: string): void;
    getMetrics(): any;
    getHealthStatus(): {
        status: 'healthy' | 'warning' | 'critical';
        issues: string[];
        recommendations: string[];
    };
    resetMetrics(): void;
    getPerformanceRecommendations(): string[];
    private updateAverageResponseTime;
    private getPercentile;
    private cleanupOldMetrics;
}
