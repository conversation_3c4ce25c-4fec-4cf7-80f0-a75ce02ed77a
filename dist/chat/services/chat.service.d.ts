import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { ChatPerformanceMonitorService } from './chat-performance-monitor.service';
import { ChatCacheService } from './chat-cache.service';
import { EntitiesService } from '../../entities/entities.service';
import { ILlmService } from '../../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';
import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationHistoryResponseDto } from '../dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from '../dto/get-conversation-history.dto';
export declare class ChatService {
    private readonly conversationManager;
    private readonly chatErrorHandler;
    private readonly llmFailover;
    private readonly performanceMonitor;
    private readonly cacheService;
    private readonly entitiesService;
    private readonly llmService;
    private readonly llmFactoryService;
    private readonly logger;
    constructor(conversationManager: ConversationManagerService, chatErrorHandler: ChatErrorHandlerService, llmFailover: LlmFailoverService, performanceMonitor: ChatPerformanceMonitorService, cacheService: ChatCacheService, entitiesService: EntitiesService, llmService: ILlmService, llmFactoryService: LlmFactoryService);
    sendMessage(userId: string, sendChatMessageDto: SendChatMessageDto): Promise<ChatResponseDto>;
    getConversationHistory(userId: string, sessionId: string, getHistoryDto: GetConversationHistoryDto): Promise<ConversationHistoryResponseDto>;
    endConversation(userId: string, sessionId: string): Promise<void>;
    getUserActiveSessions(userId: string): Promise<string[]>;
    private discoverRelevantEntities;
    getPerformanceMetrics(): any;
    getPerformanceHealth(): any;
    getCacheStats(): any;
    clearCaches(): void;
    private getCurrentLlmProvider;
}
