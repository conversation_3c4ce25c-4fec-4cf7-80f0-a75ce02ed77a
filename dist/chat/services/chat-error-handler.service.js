"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ChatErrorHandlerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatErrorHandlerService = void 0;
const common_1 = require("@nestjs/common");
let ChatErrorHandlerService = ChatErrorHandlerService_1 = class ChatErrorHandlerService {
    constructor() {
        this.logger = new common_1.Logger(ChatErrorHandlerService_1.name);
    }
    handleLlmError(error, context, userMessage) {
        this.logger.error('LLM service error', {
            error: error.message,
            stack: error.stack,
            sessionId: context.sessionId,
            userId: context.userId,
            conversationStage: context.conversationStage,
        });
        if (this.isRateLimitError(error)) {
            return this.createRateLimitFallbackResponse(context);
        }
        if (this.isApiKeyError(error)) {
            return this.createApiKeyFallbackResponse(context);
        }
        if (this.isNetworkError(error)) {
            return this.createNetworkFallbackResponse(context);
        }
        if (this.isQuotaExceededError(error)) {
            return this.createQuotaFallbackResponse(context);
        }
        return this.createGenericLlmFallbackResponse(context, userMessage);
    }
    handleConversationStateError(error, sessionId, userId) {
        this.logger.error('Conversation state error', {
            error: error.message,
            stack: error.stack,
            sessionId,
            userId,
        });
        return {
            message: "I apologize, but I'm having trouble accessing our conversation history. Let's start fresh - how can I help you find the perfect AI tools today?",
            session_id: sessionId,
            conversation_stage: 'greeting',
            should_transition_to_recommendations: false,
            follow_up_questions: [
                "What type of AI tools are you looking for?",
                "What's your main use case or project?",
                "Are you looking for free or paid solutions?"
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    handleEntityDiscoveryError(error, context, userMessage) {
        this.logger.error('Entity discovery error', {
            error: error.message,
            stack: error.stack,
            sessionId: context.sessionId,
            userMessage,
        });
        return {
            message: "I'm having some trouble searching our AI tools database right now, but I can still help you! Could you tell me more specifically what you're looking for? For example, are you interested in tools for content creation, data analysis, coding assistance, or something else?",
            session_id: context.sessionId,
            conversation_stage: context.conversationStage,
            should_transition_to_recommendations: false,
            follow_up_questions: [
                "What specific task do you want to accomplish?",
                "What industry or field are you working in?",
                "Do you have any budget constraints?"
            ],
            suggested_actions: [
                {
                    type: 'ask_question',
                    label: 'Tell me about your specific use case',
                    data: { prompt: 'Please describe what you want to accomplish in more detail' }
                }
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    handleAuthError(error, sessionId) {
        this.logger.error('Authentication error', {
            error: error.message,
            sessionId,
        });
        throw new Error('Authentication required. Please log in to continue using the chat service.');
    }
    handleValidationError(error, sessionId) {
        this.logger.error('Validation error', {
            error: error.message,
            sessionId,
        });
        throw new Error('Invalid input provided. Please check your message and try again.');
    }
    createCriticalErrorFallback(sessionId, error) {
        this.logger.error('Critical chat service error', {
            error: error.message,
            stack: error.stack,
            sessionId,
        });
        return {
            message: "I'm experiencing some technical difficulties right now. While I work on getting back to full capacity, you might want to try our recommendations endpoint directly, or feel free to browse our AI tools catalog. I apologize for the inconvenience!",
            session_id: sessionId,
            conversation_stage: 'greeting',
            should_transition_to_recommendations: true,
            suggested_actions: [
                {
                    type: 'get_recommendations',
                    label: 'Get AI tool recommendations',
                    data: { endpoint: '/recommendations' }
                }
            ],
            follow_up_questions: [
                "Would you like to try our recommendations feature instead?",
                "Can you describe what you're looking for in a few words?"
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    isRateLimitError(error) {
        return error.message?.includes('rate limit') ||
            error.message?.includes('too many requests') ||
            error.status === 429;
    }
    isApiKeyError(error) {
        return error.message?.includes('API key') ||
            error.message?.includes('authentication') ||
            error.message?.includes('unauthorized') ||
            error.status === 401;
    }
    isNetworkError(error) {
        return error.code === 'ECONNREFUSED' ||
            error.code === 'ENOTFOUND' ||
            error.code === 'ETIMEDOUT' ||
            error.message?.includes('network') ||
            error.message?.includes('connection');
    }
    isQuotaExceededError(error) {
        return error.message?.includes('quota') ||
            error.message?.includes('billing') ||
            error.message?.includes('usage limit');
    }
    createRateLimitFallbackResponse(context) {
        return {
            message: "I'm getting a lot of requests right now! Let me take a moment to process your message. In the meantime, could you tell me more about what specific type of AI tool you're looking for?",
            session_id: context.sessionId,
            conversation_stage: context.conversationStage,
            should_transition_to_recommendations: false,
            follow_up_questions: [
                "What's your primary use case?",
                "Are you looking for free or paid tools?",
                "What's your technical experience level?"
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'RATE_LIMITED_FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    createApiKeyFallbackResponse(context) {
        return {
            message: "I'm having some technical issues with my AI processing right now. But don't worry - I can still help you find great AI tools! Could you describe what you're trying to accomplish?",
            session_id: context.sessionId,
            conversation_stage: context.conversationStage,
            should_transition_to_recommendations: true,
            suggested_actions: [
                {
                    type: 'get_recommendations',
                    label: 'Browse AI tools directly',
                    data: { endpoint: '/recommendations' }
                }
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'API_KEY_FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    createNetworkFallbackResponse(context) {
        return {
            message: "I'm experiencing some connectivity issues, but I'm still here to help! While I work on reconnecting, could you tell me about your AI tool needs? I might be able to point you in the right direction.",
            session_id: context.sessionId,
            conversation_stage: context.conversationStage,
            should_transition_to_recommendations: false,
            follow_up_questions: [
                "What type of work are you trying to automate?",
                "Are you looking for tools for a specific industry?",
                "Do you need help with content, code, data, or something else?"
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'NETWORK_FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    createQuotaFallbackResponse(context) {
        return {
            message: "I've reached my processing limit for now, but I can still assist you! Let's try a different approach - you can browse our AI tools directly or tell me specifically what you're looking for and I'll do my best to help.",
            session_id: context.sessionId,
            conversation_stage: context.conversationStage,
            should_transition_to_recommendations: true,
            suggested_actions: [
                {
                    type: 'get_recommendations',
                    label: 'Get recommendations directly',
                    data: { endpoint: '/recommendations' }
                }
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'QUOTA_FALLBACK',
            },
            generated_at: new Date(),
        };
    }
    createGenericLlmFallbackResponse(context, userMessage) {
        const stageResponses = {
            greeting: "Welcome! I'm here to help you discover amazing AI tools. What kind of tasks are you looking to accomplish?",
            discovery: "I'm having a small technical hiccup, but let's keep exploring! What specific features are most important to you in an AI tool?",
            refinement: "Let me help you narrow down your options. Based on what we've discussed, what's your top priority - ease of use, specific features, or budget?",
            recommendation: "I want to make sure I give you the best recommendations. Could you summarize your key requirements one more time?",
            comparison: "I'm having trouble processing comparisons right now, but I can still help you evaluate your options. What criteria matter most to you?"
        };
        return {
            message: stageResponses[context.conversationStage] || stageResponses.discovery,
            session_id: context.sessionId,
            conversation_stage: context.conversationStage,
            should_transition_to_recommendations: context.conversationStage === 'recommendation',
            follow_up_questions: [
                "What's your main goal with AI tools?",
                "What's your budget range?",
                "How technical do you want the solution to be?"
            ],
            metadata: {
                response_time: 0,
                llm_provider: 'GENERIC_FALLBACK',
            },
            generated_at: new Date(),
        };
    }
};
exports.ChatErrorHandlerService = ChatErrorHandlerService;
exports.ChatErrorHandlerService = ChatErrorHandlerService = ChatErrorHandlerService_1 = __decorate([
    (0, common_1.Injectable)()
], ChatErrorHandlerService);
//# sourceMappingURL=chat-error-handler.service.js.map