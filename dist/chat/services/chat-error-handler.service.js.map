{"version": 3, "file": "chat-error-handler.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/chat-error-handler.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAS7C,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAA7B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAwTrE,CAAC;IAnTC,cAAc,CACZ,KAAU,EACV,OAA4B,EAC5B,WAAmB;QAEnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;YACrC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;SAC7C,CAAC,CAAC;QAGH,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QAGD,OAAO,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAKD,4BAA4B,CAC1B,KAAU,EACV,SAAiB,EACjB,MAAc;QAEd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YAC5C,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS;YACT,MAAM;SACP,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,iJAAiJ;YAC1J,UAAU,EAAE,SAAS;YACrB,kBAAkB,EAAE,UAAU;YAC9B,oCAAoC,EAAE,KAAK;YAC3C,mBAAmB,EAAE;gBACnB,4CAA4C;gBAC5C,uCAAuC;gBACvC,6CAA6C;aAC9C;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,UAAU;aACzB;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAKD,0BAA0B,CACxB,KAAU,EACV,OAA4B,EAC5B,WAAmB;QAEnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;YAC1C,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW;SACZ,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,+QAA+Q;YACxR,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;YAC7C,oCAAoC,EAAE,KAAK;YAC3C,mBAAmB,EAAE;gBACnB,+CAA+C;gBAC/C,4CAA4C;gBAC5C,qCAAqC;aACtC;YACD,iBAAiB,EAAE;gBACjB;oBACE,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,sCAAsC;oBAC7C,IAAI,EAAE,EAAE,MAAM,EAAE,4DAA4D,EAAE;iBAC/E;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,UAAU;aACzB;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAKD,eAAe,CAAC,KAAU,EAAE,SAAkB;QAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;YACxC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,SAAS;SACV,CAAC,CAAC;QAEH,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;IAChG,CAAC;IAKD,qBAAqB,CAAC,KAAU,EAAE,SAAkB;QAClD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;YACpC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,SAAS;SACV,CAAC,CAAC;QAEH,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;IACtF,CAAC;IAKD,2BAA2B,CACzB,SAAiB,EACjB,KAAU;QAEV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC/C,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS;SACV,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,qPAAqP;YAC9P,UAAU,EAAE,SAAS;YACrB,kBAAkB,EAAE,UAAU;YAC9B,oCAAoC,EAAE,IAAI;YAC1C,iBAAiB,EAAE;gBACjB;oBACE,IAAI,EAAE,qBAAqB;oBAC3B,KAAK,EAAE,6BAA6B;oBACpC,IAAI,EAAE,EAAE,QAAQ,EAAE,kBAAkB,EAAE;iBACvC;aACF;YACD,mBAAmB,EAAE;gBACnB,4DAA4D;gBAC5D,0DAA0D;aAC3D;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,UAAU;aACzB;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAGO,gBAAgB,CAAC,KAAU;QACjC,OAAO,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC;YACrC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,mBAAmB,CAAC;YAC5C,KAAK,CAAC,MAAM,KAAK,GAAG,CAAC;IAC9B,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,OAAO,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC;YAClC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,gBAAgB,CAAC;YACzC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,cAAc,CAAC;YACvC,KAAK,CAAC,MAAM,KAAK,GAAG,CAAC;IAC9B,CAAC;IAEO,cAAc,CAAC,KAAU;QAC/B,OAAO,KAAK,CAAC,IAAI,KAAK,cAAc;YAC7B,KAAK,CAAC,IAAI,KAAK,WAAW;YAC1B,KAAK,CAAC,IAAI,KAAK,WAAW;YAC1B,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC;YAClC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAEO,oBAAoB,CAAC,KAAU;QACrC,OAAO,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;YAChC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC;YAClC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAGO,+BAA+B,CAAC,OAA4B;QAClE,OAAO;YACL,OAAO,EAAE,wLAAwL;YACjM,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;YAC7C,oCAAoC,EAAE,KAAK;YAC3C,mBAAmB,EAAE;gBACnB,+BAA+B;gBAC/B,yCAAyC;gBACzC,yCAAyC;aAC1C;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,uBAAuB;aACtC;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAEO,4BAA4B,CAAC,OAA4B;QAC/D,OAAO;YACL,OAAO,EAAE,oLAAoL;YAC7L,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;YAC7C,oCAAoC,EAAE,IAAI;YAC1C,iBAAiB,EAAE;gBACjB;oBACE,IAAI,EAAE,qBAAqB;oBAC3B,KAAK,EAAE,0BAA0B;oBACjC,IAAI,EAAE,EAAE,QAAQ,EAAE,kBAAkB,EAAE;iBACvC;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,kBAAkB;aACjC;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,OAA4B;QAChE,OAAO;YACL,OAAO,EAAE,uMAAuM;YAChN,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;YAC7C,oCAAoC,EAAE,KAAK;YAC3C,mBAAmB,EAAE;gBACnB,+CAA+C;gBAC/C,oDAAoD;gBACpD,+DAA+D;aAChE;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,kBAAkB;aACjC;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAEO,2BAA2B,CAAC,OAA4B;QAC9D,OAAO;YACL,OAAO,EAAE,0NAA0N;YACnO,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;YAC7C,oCAAoC,EAAE,IAAI;YAC1C,iBAAiB,EAAE;gBACjB;oBACE,IAAI,EAAE,qBAAqB;oBAC3B,KAAK,EAAE,8BAA8B;oBACrC,IAAI,EAAE,EAAE,QAAQ,EAAE,kBAAkB,EAAE;iBACvC;aACF;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,gBAAgB;aAC/B;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;IAEO,gCAAgC,CACtC,OAA4B,EAC5B,WAAmB;QAGnB,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,4GAA4G;YACtH,SAAS,EAAE,gIAAgI;YAC3I,UAAU,EAAE,gJAAgJ;YAC5J,cAAc,EAAE,mHAAmH;YACnI,UAAU,EAAE,wIAAwI;SACrJ,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,cAAc,CAAC,SAAS;YAC9E,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,kBAAkB,EAAE,OAAO,CAAC,iBAAiB;YAC7C,oCAAoC,EAAE,OAAO,CAAC,iBAAiB,KAAK,gBAAgB;YACpF,mBAAmB,EAAE;gBACnB,sCAAsC;gBACtC,2BAA2B;gBAC3B,+CAA+C;aAChD;YACD,QAAQ,EAAE;gBACR,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,kBAAkB;aACjC;YACD,YAAY,EAAE,IAAI,IAAI,EAAE;SACzB,CAAC;IACJ,CAAC;CACF,CAAA;AAzTY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;GACA,uBAAuB,CAyTnC"}