import { ReqUserObject } from '../auth/strategies/jwt.strategy';
import { ChatService } from './services/chat.service';
import { SendChatMessageDto } from './dto/send-chat-message.dto';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ConversationHistoryResponseDto } from './dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from './dto/get-conversation-history.dto';
export declare class ChatController {
    private readonly chatService;
    constructor(chatService: ChatService);
    sendMessage(sendChatMessageDto: SendChatMessageDto, reqUser: ReqUserObject): Promise<ChatResponseDto>;
    getConversationHistory(sessionId: string, getHistoryDto: GetConversationHistoryDto, reqUser: ReqUserObject): Promise<ConversationHistoryResponseDto>;
    endConversation(sessionId: string, reqUser: ReqUserObject): Promise<void>;
    getUserActiveSessions(reqUser: ReqUserObject): Promise<{
        sessions: string[];
        total: number;
    }>;
    getPerformanceMetrics(reqUser: ReqUserObject): Promise<any>;
    clearCaches(reqUser: ReqUserObject): Promise<void>;
}
