import { ConversationContext } from '../../common/llm/interfaces/llm.service.interface';
export interface IConversationStateService {
    setConversationContext(sessionId: string, context: ConversationContext, ttlSeconds?: number): Promise<void>;
    getConversationContext(sessionId: string): Promise<ConversationContext | null>;
    deleteConversationContext(sessionId: string): Promise<void>;
    hasConversationContext(sessionId: string): Promise<boolean>;
    getUserActiveSessions(userId: string): Promise<string[]>;
    cleanupExpiredConversations(): Promise<number>;
    getStats(): Promise<{
        totalSessions: number;
        activeSessions: number;
        memoryUsage?: number;
    }>;
}
export interface ConversationStateConfig {
    defaultTtlSeconds: number;
    maxSessions: number;
    cleanupIntervalMs: number;
    enableMetrics: boolean;
}
export interface ConversationSession {
    sessionId: string;
    userId: string;
    context: ConversationContext;
    createdAt: Date;
    lastAccessedAt: Date;
    expiresAt: Date;
}
