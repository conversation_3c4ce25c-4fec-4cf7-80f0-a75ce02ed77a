import { ReviewsService } from './reviews.service';
import { UpdateReviewStatusDto } from './dto/update-review-status.dto';
import { ListAdminReviewsDto } from '../admin/reviews/dto/list-admin-reviews.dto';
export declare class AdminReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    findAll(listAdminReviewsDto: ListAdminReviewsDto): Promise<import("../common/interfaces/paginated-response.interface").PaginatedResponse<{
        title: string | null;
        id: string;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        createdAt: Date;
        updatedAt: Date;
        content: string | null;
        userId: string;
        entityId: string;
        rating: number;
        moderatorId: string | null;
        moderationNotes: string | null;
        upvotes: number;
        downvotes: number;
    } & {
        user: {
            id: string;
            username: string | null;
            email: string;
        };
        entity: {
            id: string;
            name: string;
        };
    }>>;
    updateReviewStatus(id: string, updateReviewStatusDto: UpdateReviewStatusDto, req: any): Promise<{
        title: string | null;
        id: string;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        createdAt: Date;
        updatedAt: Date;
        content: string | null;
        userId: string;
        entityId: string;
        rating: number;
        moderatorId: string | null;
        moderationNotes: string | null;
        upvotes: number;
        downvotes: number;
    }>;
    adminDeleteReview(id: string, req: any): Promise<void>;
}
