"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReviewsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("generated/prisma");
const common_2 = require("@nestjs/common");
const activity_logger_service_1 = require("../common/activity-logger.service");
const validation_service_1 = require("../common/validation.service");
let ReviewsService = ReviewsService_1 = class ReviewsService {
    constructor(prisma, activityLogger, validationService) {
        this.prisma = prisma;
        this.activityLogger = activityLogger;
        this.validationService = validationService;
        this.logger = new common_2.Logger(ReviewsService_1.name);
    }
    async createReview(userId, entityId, createReviewDto) {
        const { rating, title, reviewText } = createReviewDto;
        await this.validationService.validateReviewSubmission(userId, entityId);
        const entity = await this.prisma.entity.findUnique({
            where: { id: entityId },
            select: { id: true, name: true, slug: true },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${entityId}" not found.`);
        }
        const existingReview = await this.prisma.review.findUnique({
            where: {
                entityId_userId: {
                    entityId,
                    userId,
                },
            },
        });
        if (existingReview) {
            throw new common_1.ConflictException('You have already reviewed this entity.');
        }
        try {
            const newReview = await this.prisma.review.create({
                data: {
                    userId,
                    entityId,
                    rating,
                    title,
                    content: reviewText,
                    status: prisma_1.ReviewStatus.PENDING,
                },
            });
            await this.activityLogger.logReviewActivity(userId, entityId, entity.name, entity.slug, 'created');
            return newReview;
        }
        catch (error) {
            console.error('Error creating review:', error);
            throw new common_1.InternalServerErrorException('Could not submit review.');
        }
    }
    async updateUserReview(reviewId, userId, updateReviewDto) {
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
            include: {
                entity: {
                    select: { id: true, name: true, slug: true },
                },
            },
        });
        if (!review) {
            throw new common_1.NotFoundException(`Review with ID "${reviewId}" not found.`);
        }
        if (review.userId !== userId) {
            throw new common_1.ForbiddenException('You are not authorized to update this review.');
        }
        if (review.status !== prisma_1.ReviewStatus.PENDING) {
            throw new common_1.ForbiddenException('Only pending reviews can be updated.');
        }
        try {
            const updatedReview = await this.prisma.review.update({
                where: { id: reviewId },
                data: {
                    ...updateReviewDto,
                    updatedAt: new Date(),
                },
            });
            if (review.entity) {
                await this.activityLogger.logReviewActivity(userId, review.entityId, review.entity.name, review.entity.slug, 'updated');
            }
            return updatedReview;
        }
        catch (error) {
            console.error(`Error updating review ${reviewId}:`, error);
            throw new common_1.InternalServerErrorException('Could not update review.');
        }
    }
    async deleteUserReview(reviewId, userId) {
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
            include: {
                entity: {
                    select: { id: true, name: true, slug: true },
                },
            },
        });
        if (!review) {
            return;
        }
        if (review.userId !== userId) {
            throw new common_1.ForbiddenException('You are not authorized to delete this review.');
        }
        try {
            await this.prisma.review.delete({
                where: { id: reviewId },
            });
            if (review && review.entity) {
                await this.activityLogger.logReviewActivity(userId, review.entityId, review.entity.name, review.entity.slug, 'deleted');
            }
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                return;
            }
            console.error(`Error deleting review ${reviewId}:`, error);
            throw new common_1.InternalServerErrorException('Could not delete review.');
        }
    }
    async getApprovedReviewsForEntity(entityId, listReviewsDto) {
        const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = prisma_1.Prisma.SortOrder.desc } = listReviewsDto;
        const skip = (page - 1) * limit;
        const entityExists = await this.prisma.entity.findUnique({
            where: { id: entityId },
            select: { id: true },
        });
        if (!entityExists) {
            throw new common_1.NotFoundException(`Entity with ID "${entityId}" not found.`);
        }
        try {
            const [reviews, total] = await this.prisma.$transaction([
                this.prisma.review.findMany({
                    where: {
                        entityId,
                        status: prisma_1.ReviewStatus.APPROVED,
                    },
                    include: {
                        user: {
                            select: {
                                id: true,
                                username: true,
                                profilePictureUrl: true,
                            },
                        },
                    },
                    orderBy: {
                        [sortBy]: sortOrder,
                    },
                    skip,
                    take: limit,
                }),
                this.prisma.review.count({
                    where: {
                        entityId,
                        status: prisma_1.ReviewStatus.APPROVED,
                    },
                }),
            ]);
            return {
                data: reviews,
                total,
                page,
                limit,
            };
        }
        catch (error) {
            console.error('Error fetching approved reviews:', error);
            throw new common_1.InternalServerErrorException('Could not fetch reviews.');
        }
    }
    async updateReviewStatus(reviewId, newStatus, adminUser) {
        this.logger.log(`Admin ${adminUser.email} (ID: ${adminUser.id}) attempting to update review ${reviewId} to status ${newStatus}`);
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
        });
        if (!review) {
            this.logger.warn(`Review with ID ${reviewId} not found for status update.`);
            throw new common_1.NotFoundException(`Review with ID ${reviewId} not found.`);
        }
        const updatedReview = await this.prisma.review.update({
            where: { id: reviewId },
            data: {
                status: newStatus,
                updatedAt: new Date(),
            },
            include: {
                user: true,
                entity: true,
            },
        });
        this.logger.log(`Review ${reviewId} status successfully updated to ${newStatus} by admin ${adminUser.email}`);
        return updatedReview;
    }
    async adminDeleteReview(reviewId, adminUser) {
        this.logger.log(`Admin ${adminUser.email} (ID: ${adminUser.id}) attempting to delete review ${reviewId}`);
        const review = await this.prisma.review.findUnique({
            where: { id: reviewId },
        });
        if (!review) {
            this.logger.warn(`Review with ID ${reviewId} not found for admin deletion.`);
            throw new common_1.NotFoundException(`Review with ID ${reviewId} not found.`);
        }
        try {
            await this.prisma.review.delete({
                where: { id: reviewId },
            });
            this.logger.log(`Review ${reviewId} successfully deleted by admin ${adminUser.email}`);
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
                this.logger.warn(`Review with ID ${reviewId} was already deleted (P2025).`);
                throw new common_1.NotFoundException(`Review with ID ${reviewId} not found or already deleted.`);
            }
            this.logger.error(`Error deleting review ${reviewId} by admin ${adminUser.email}:`, error.stack || error);
            throw new common_1.InternalServerErrorException('Could not delete review.');
        }
    }
    async adminFindAll(dto) {
        const { page = 1, limit = 10, status, userId, entityId } = dto;
        const skip = (page - 1) * limit;
        const where = {};
        if (status)
            where.status = status;
        if (userId)
            where.userId = userId;
        if (entityId)
            where.entityId = entityId;
        const [reviews, total] = await this.prisma.$transaction([
            this.prisma.review.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: { select: { id: true, username: true, email: true } },
                    entity: { select: { id: true, name: true } },
                },
            }),
            this.prisma.review.count({ where }),
        ]);
        return {
            data: reviews,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
};
exports.ReviewsService = ReviewsService;
exports.ReviewsService = ReviewsService = ReviewsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        activity_logger_service_1.ActivityLoggerService,
        validation_service_1.ValidationService])
], ReviewsService);
//# sourceMappingURL=reviews.service.js.map