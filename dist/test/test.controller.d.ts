import { TestService } from './test.service';
export declare class TestController {
    private readonly testService;
    constructor(testService: TestService);
    testEntityTypeCrud(): Promise<{
        createReadResult: {
            createdType: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                description: string | null;
                iconUrl: string | null;
            };
            readType: {
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                description: string | null;
                iconUrl: string | null;
            } | null;
            allTypesCount: number;
        };
        updateResult: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            description: string | null;
            iconUrl: string | null;
        };
        deleteResult: {
            message: string;
            deleted: boolean;
        };
    }>;
    testEntityRelation(): Promise<{
        entityType: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            description: string | null;
            iconUrl: string | null;
        };
    } & {
        name: string;
        id: string;
        status: import("@generated-prisma").$Enums.EntityStatus;
        createdAt: Date;
        updatedAt: Date;
        socialLinks: import("@generated-prisma/runtime/library").JsonValue | null;
        slug: string | null;
        websiteUrl: string | null;
        entityTypeId: string;
        shortDescription: string | null;
        description: string | null;
        logoUrl: string | null;
        documentationUrl: string | null;
        contactUrl: string | null;
        privacyPolicyUrl: string | null;
        foundedYear: number | null;
        submitterId: string;
        legacyId: string | null;
        reviewCount: number;
        avgRating: number;
        metaTitle: string | null;
        metaDescription: string | null;
        scrapedReviewSentimentLabel: string | null;
        scrapedReviewSentimentScore: number | null;
        scrapedReviewCount: number | null;
        employeeCountRange: import("@generated-prisma").$Enums.EmployeeCountRange | null;
        fundingStage: import("@generated-prisma").$Enums.FundingStage | null;
        locationSummary: string | null;
        refLink: string | null;
        affiliateStatus: import("@generated-prisma").$Enums.AffiliateStatus | null;
    }>;
    testEntityTagM2MRelation(): Promise<{
        finalEntity: {
            entityTags: ({
                tag: {
                    name: string;
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    slug: string;
                    description: string | null;
                };
            } & {
                id: string;
                entityId: string;
                createdAt: Date;
                tagId: string;
                assignedBy: string;
            })[];
        } & {
            name: string;
            id: string;
            status: import("@generated-prisma").$Enums.EntityStatus;
            createdAt: Date;
            updatedAt: Date;
            socialLinks: import("@generated-prisma/runtime/library").JsonValue | null;
            slug: string | null;
            websiteUrl: string | null;
            entityTypeId: string;
            shortDescription: string | null;
            description: string | null;
            logoUrl: string | null;
            documentationUrl: string | null;
            contactUrl: string | null;
            privacyPolicyUrl: string | null;
            foundedYear: number | null;
            submitterId: string;
            legacyId: string | null;
            reviewCount: number;
            avgRating: number;
            metaTitle: string | null;
            metaDescription: string | null;
            scrapedReviewSentimentLabel: string | null;
            scrapedReviewSentimentScore: number | null;
            scrapedReviewCount: number | null;
            employeeCountRange: import("@generated-prisma").$Enums.EmployeeCountRange | null;
            fundingStage: import("@generated-prisma").$Enums.FundingStage | null;
            locationSummary: string | null;
            refLink: string | null;
            affiliateStatus: import("@generated-prisma").$Enums.AffiliateStatus | null;
        };
        tagSlugs: string[];
    }>;
    testEnumConstraints(): Promise<{
        message: string;
    }>;
    testUniqueConstraints(): Promise<{
        message: string;
    }>;
    testOptionalDefaultsAndTimestamps(): Promise<{
        message: string;
        initial: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            description: string | null;
            iconUrl: string | null;
        };
        updated: {
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            description: string | null;
            iconUrl: string | null;
        };
    }>;
}
