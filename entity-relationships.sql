-- =====================================================
-- ENTITY RELATIONSHIPS (Categories, Tags, Features)
-- Run this AFTER sample-entities.sql
-- =====================================================

DO $$ 
DECLARE
    chatgpt_id UUID;
    claude_id UUID;
    midjourney_id UUID;
    copilot_id UUID;
    huggingface_id UUID;
    openai_api_id UUID;
    stable_diffusion_id UUID;
    notion_ai_id UUID;
    
    ml_category_id UUID;
    nlp_category_id UUID;
    cv_category_id UUID;
    dev_tools_category_id UUID;
    
    free_tag_id UUID;
    api_tag_id UUID;
    enterprise_tag_id UUID;
    beginner_tag_id UUID;
    open_source_tag_id UUID;
    
    text_gen_feature_id UUID;
    image_gen_feature_id UUID;
    code_gen_feature_id UUID;
    analysis_feature_id UUID;
BEGIN
    -- Get entity IDs
    SELECT id INTO chatgpt_id FROM entities WHERE slug = 'chatgpt';
    SELECT id INTO claude_id FROM entities WHERE slug = 'claude';
    SELECT id INTO midjourney_id FROM entities WHERE slug = 'midjourney';
    SELECT id INTO copilot_id FROM entities WHERE slug = 'github-copilot';
    SELECT id INTO huggingface_id FROM entities WHERE slug = 'hugging-face';
    SELECT id INTO openai_api_id FROM entities WHERE slug = 'openai-api';
    SELECT id INTO stable_diffusion_id FROM entities WHERE slug = 'stable-diffusion';
    SELECT id INTO notion_ai_id FROM entities WHERE slug = 'notion-ai';

    -- Get category IDs (if categories table exists)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        SELECT id INTO ml_category_id FROM categories WHERE slug = 'machine-learning';
        SELECT id INTO nlp_category_id FROM categories WHERE slug = 'natural-language-processing';
        SELECT id INTO cv_category_id FROM categories WHERE slug = 'computer-vision';
        SELECT id INTO dev_tools_category_id FROM categories WHERE slug = 'development-tools';

        -- Insert entity-category relationships
        INSERT INTO "public"."entity_categories" ("entity_id", "category_id") VALUES
            (chatgpt_id, nlp_category_id),
            (chatgpt_id, ml_category_id),
            (claude_id, nlp_category_id),
            (claude_id, ml_category_id),
            (midjourney_id, cv_category_id),
            (midjourney_id, ml_category_id),
            (copilot_id, dev_tools_category_id),
            (copilot_id, ml_category_id),
            (huggingface_id, ml_category_id),
            (openai_api_id, nlp_category_id),
            (openai_api_id, ml_category_id),
            (stable_diffusion_id, cv_category_id),
            (stable_diffusion_id, ml_category_id),
            (notion_ai_id, nlp_category_id)
        ON CONFLICT DO NOTHING;
    END IF;

    -- Get tag IDs (if tags table exists)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        SELECT id INTO free_tag_id FROM tags WHERE slug = 'free';
        SELECT id INTO api_tag_id FROM tags WHERE slug = 'api-available';
        SELECT id INTO enterprise_tag_id FROM tags WHERE slug = 'enterprise';
        SELECT id INTO beginner_tag_id FROM tags WHERE slug = 'beginner-friendly';
        SELECT id INTO open_source_tag_id FROM tags WHERE slug = 'open-source';

        -- Insert entity-tag relationships
        INSERT INTO "public"."entity_tags" ("entity_id", "tag_id") VALUES
            (chatgpt_id, beginner_tag_id),
            (chatgpt_id, enterprise_tag_id),
            (claude_id, beginner_tag_id),
            (midjourney_id, beginner_tag_id),
            (copilot_id, enterprise_tag_id),
            (copilot_id, api_tag_id),
            (huggingface_id, free_tag_id),
            (huggingface_id, open_source_tag_id),
            (openai_api_id, api_tag_id),
            (openai_api_id, enterprise_tag_id),
            (stable_diffusion_id, free_tag_id),
            (stable_diffusion_id, open_source_tag_id),
            (notion_ai_id, beginner_tag_id),
            (notion_ai_id, enterprise_tag_id)
        ON CONFLICT DO NOTHING;
    END IF;

    -- Get feature IDs (if features table exists)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'features') THEN
        SELECT id INTO text_gen_feature_id FROM features WHERE slug = 'text-generation';
        SELECT id INTO image_gen_feature_id FROM features WHERE slug = 'image-generation';
        SELECT id INTO code_gen_feature_id FROM features WHERE slug = 'code-generation';
        SELECT id INTO analysis_feature_id FROM features WHERE slug = 'data-analysis';

        -- Insert entity-feature relationships
        INSERT INTO "public"."entity_features" ("entity_id", "feature_id") VALUES
            (chatgpt_id, text_gen_feature_id),
            (chatgpt_id, code_gen_feature_id),
            (chatgpt_id, analysis_feature_id),
            (claude_id, text_gen_feature_id),
            (claude_id, analysis_feature_id),
            (midjourney_id, image_gen_feature_id),
            (copilot_id, code_gen_feature_id),
            (openai_api_id, text_gen_feature_id),
            (openai_api_id, image_gen_feature_id),
            (stable_diffusion_id, image_gen_feature_id),
            (notion_ai_id, text_gen_feature_id)
        ON CONFLICT DO NOTHING;
    END IF;

END $$;

-- Add some sample reviews for testing
DO $$ 
DECLARE
    admin_user_id UUID;
    chatgpt_id UUID;
    claude_id UUID;
    midjourney_id UUID;
BEGIN
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin' LIMIT 1;
    SELECT id INTO chatgpt_id FROM entities WHERE slug = 'chatgpt';
    SELECT id INTO claude_id FROM entities WHERE slug = 'claude';
    SELECT id INTO midjourney_id FROM entities WHERE slug = 'midjourney';

    -- Insert sample reviews (if reviews table exists)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reviews') THEN
        INSERT INTO "public"."reviews" (
            "id", "entity_id", "user_id", "rating", "title", "review_text", 
            "created_at", "updated_at"
        ) VALUES
        (
            gen_random_uuid(),
            chatgpt_id,
            admin_user_id,
            5,
            'Excellent AI Assistant',
            'ChatGPT has revolutionized how I approach writing and problem-solving. The responses are consistently helpful and well-structured.',
            NOW(),
            NOW()
        ),
        (
            gen_random_uuid(),
            claude_id,
            admin_user_id,
            5,
            'Great for Analysis',
            'Claude excels at detailed analysis and provides thoughtful, nuanced responses. Very reliable for complex tasks.',
            NOW(),
            NOW()
        ),
        (
            gen_random_uuid(),
            midjourney_id,
            admin_user_id,
            4,
            'Amazing Image Generation',
            'Midjourney creates stunning, artistic images. The quality is impressive, though it can take some practice to get the prompts right.',
            NOW(),
            NOW()
        )
        ON CONFLICT DO NOTHING;
    END IF;

END $$;

-- Update entity statistics
UPDATE entities SET 
    review_count = (SELECT COUNT(*) FROM reviews WHERE entity_id = entities.id),
    avg_rating = (SELECT COALESCE(AVG(rating), 0) FROM reviews WHERE entity_id = entities.id)
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reviews');

-- Success message
SELECT 'Entity relationships and sample data created successfully!' as result;
SELECT 'Database is now ready for testing!' as status;
