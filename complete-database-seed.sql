-- =====================================================
-- COMPLETE DATABASE SEED SCRIPT
-- Run this single script in Supabase SQL Editor to populate everything
-- =====================================================

-- PART 1: ENTITY TYPES
INSERT INTO "public"."entity_types" ("id", "name", "description", "slug", "icon_url", "created_at", "updated_at") 
VALUES 
    (gen_random_uuid(), 'AI Tool', 'Artificial Intelligence tools and platforms', 'ai-tool', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'API', 'Application Programming Interfaces and web services', 'api', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Dataset', 'Data collections and datasets for training and research', 'dataset', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Model', 'Pre-trained machine learning models', 'model', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Platform', 'Development and deployment platforms', 'platform', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Library', 'Code libraries and frameworks', 'library', NULL, NOW(), NOW()),
    (gen_random_uuid(), 'Service', 'Cloud services and SaaS platforms', 'service', NULL, NOW(), NOW())
ON CONFLICT ("name") DO NOTHING;

-- PART 2: CATEGORIES (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        INSERT INTO "public"."categories" ("id", "name", "description", "slug", "created_at", "updated_at") 
        VALUES 
            (gen_random_uuid(), 'Machine Learning', 'Tools and platforms for machine learning', 'machine-learning', NOW(), NOW()),
            (gen_random_uuid(), 'Natural Language Processing', 'NLP tools and language models', 'natural-language-processing', NOW(), NOW()),
            (gen_random_uuid(), 'Computer Vision', 'Image and video processing tools', 'computer-vision', NOW(), NOW()),
            (gen_random_uuid(), 'Data Science', 'Data analysis and visualization tools', 'data-science', NOW(), NOW()),
            (gen_random_uuid(), 'Development Tools', 'Developer tools and IDEs', 'development-tools', NOW(), NOW()),
            (gen_random_uuid(), 'Automation', 'Workflow automation and productivity tools', 'automation', NOW(), NOW()),
            (gen_random_uuid(), 'Analytics', 'Business intelligence and analytics platforms', 'analytics', NOW(), NOW()),
            (gen_random_uuid(), 'Content Creation', 'Tools for creating and editing content', 'content-creation', NOW(), NOW()),
            (gen_random_uuid(), 'Research', 'Academic and research tools', 'research', NOW(), NOW()),
            (gen_random_uuid(), 'Business Intelligence', 'BI and enterprise analytics tools', 'business-intelligence', NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING;
    END IF;
END $$;

-- PART 3: TAGS (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
        INSERT INTO "public"."tags" ("id", "name", "description", "slug", "created_at", "updated_at") 
        VALUES 
            (gen_random_uuid(), 'Free', 'Free to use tools and services', 'free', NOW(), NOW()),
            (gen_random_uuid(), 'Open Source', 'Open source projects and tools', 'open-source', NOW(), NOW()),
            (gen_random_uuid(), 'API Available', 'Tools that provide APIs', 'api-available', NOW(), NOW()),
            (gen_random_uuid(), 'Cloud-based', 'Cloud-based services', 'cloud-based', NOW(), NOW()),
            (gen_random_uuid(), 'Enterprise', 'Enterprise-grade solutions', 'enterprise', NOW(), NOW()),
            (gen_random_uuid(), 'Beginner Friendly', 'Easy to use for beginners', 'beginner-friendly', NOW(), NOW()),
            (gen_random_uuid(), 'Advanced', 'Advanced tools for experts', 'advanced', NOW(), NOW()),
            (gen_random_uuid(), 'No-Code', 'No coding required', 'no-code', NOW(), NOW()),
            (gen_random_uuid(), 'Real-time', 'Real-time processing capabilities', 'real-time', NOW(), NOW()),
            (gen_random_uuid(), 'Mobile App', 'Has mobile application', 'mobile-app', NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING;
    END IF;
END $$;

-- PART 4: FEATURES (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'features') THEN
        INSERT INTO "public"."features" ("id", "name", "description", "slug", "created_at", "updated_at") 
        VALUES 
            (gen_random_uuid(), 'Text Generation', 'Generate human-like text content', 'text-generation', NOW(), NOW()),
            (gen_random_uuid(), 'Image Generation', 'Create images from text descriptions', 'image-generation', NOW(), NOW()),
            (gen_random_uuid(), 'Code Generation', 'Generate code in various programming languages', 'code-generation', NOW(), NOW()),
            (gen_random_uuid(), 'Data Analysis', 'Analyze and visualize data', 'data-analysis', NOW(), NOW()),
            (gen_random_uuid(), 'Translation', 'Translate text between languages', 'translation', NOW(), NOW()),
            (gen_random_uuid(), 'Summarization', 'Summarize long text content', 'summarization', NOW(), NOW()),
            (gen_random_uuid(), 'Question Answering', 'Answer questions based on context', 'question-answering', NOW(), NOW()),
            (gen_random_uuid(), 'Speech Recognition', 'Convert speech to text', 'speech-recognition', NOW(), NOW()),
            (gen_random_uuid(), 'Text-to-Speech', 'Convert text to speech', 'text-to-speech', NOW(), NOW()),
            (gen_random_uuid(), 'Sentiment Analysis', 'Analyze sentiment in text', 'sentiment-analysis', NOW(), NOW())
        ON CONFLICT ("name") DO NOTHING;
    END IF;
END $$;

-- PART 5: APP SETTINGS
INSERT INTO "public"."app_settings" ("key", "value", "description", "created_at", "updated_at") 
VALUES 
    ('default_llm_provider', 'openai', 'Default LLM provider for the application', NOW(), NOW()),
    ('max_entities_per_page', '20', 'Maximum number of entities to show per page', NOW(), NOW()),
    ('enable_user_submissions', 'true', 'Allow users to submit new entities', NOW(), NOW()),
    ('enable_tool_requests', 'true', 'Allow users to request new tools', NOW(), NOW()),
    ('maintenance_mode', 'false', 'Enable maintenance mode', NOW(), NOW()),
    ('featured_entities_count', '6', 'Number of featured entities to show on homepage', NOW(), NOW()),
    ('enable_reviews', 'true', 'Allow users to write reviews', NOW(), NOW()),
    ('min_review_length', '50', 'Minimum review length in characters', NOW(), NOW())
ON CONFLICT ("key") DO NOTHING;

-- PART 6: SAMPLE ADMIN USER AND ENTITIES
DO $$
DECLARE
    admin_user_id UUID;
    ai_tool_type_id UUID;
    platform_type_id UUID;
    api_type_id UUID;
BEGIN
    -- Create a sample admin user
    INSERT INTO "public"."users" (
        "id", "auth_user_id", "username", "display_name", "email",
        "role", "status", "created_at", "updated_at"
    ) VALUES (
        gen_random_uuid(),
        gen_random_uuid(), -- This will be replaced when you create a real user
        'admin',
        'System Administrator',
        '<EMAIL>',
        'ADMIN',
        'ACTIVE',
        NOW(),
        NOW()
    ) ON CONFLICT ("auth_user_id") DO NOTHING
    RETURNING id INTO admin_user_id;

    -- If user already exists, get their ID
    IF admin_user_id IS NULL THEN
        SELECT id INTO admin_user_id FROM users WHERE username = 'admin' LIMIT 1;
    END IF;

    -- Get entity type IDs
    SELECT id INTO ai_tool_type_id FROM entity_types WHERE slug = 'ai-tool' LIMIT 1;
    SELECT id INTO platform_type_id FROM entity_types WHERE slug = 'platform' LIMIT 1;
    SELECT id INTO api_type_id FROM entity_types WHERE slug = 'api' LIMIT 1;

    -- Insert sample entities
    INSERT INTO "public"."entities" (
        "id", "name", "slug", "website_url", "entity_type_id",
        "short_description", "description", "status", "submitter_id",
        "created_at", "updated_at"
    ) VALUES
    (
        gen_random_uuid(),
        'ChatGPT',
        'chatgpt',
        'https://chat.openai.com',
        ai_tool_type_id,
        'AI-powered conversational assistant by OpenAI',
        'ChatGPT is a large language model developed by OpenAI that can engage in conversational interactions, answer questions, help with writing, coding, analysis, and many other tasks.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Claude',
        'claude',
        'https://claude.ai',
        ai_tool_type_id,
        'AI assistant by Anthropic focused on helpful, harmless, and honest interactions',
        'Claude is an AI assistant created by Anthropic. It is designed to be helpful, harmless, and honest, with strong capabilities in analysis, writing, math, coding, and creative tasks.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Midjourney',
        'midjourney',
        'https://midjourney.com',
        ai_tool_type_id,
        'AI-powered image generation tool',
        'Midjourney is an independent research lab that produces an AI program that creates images from textual descriptions. It is known for its artistic and creative image generation capabilities.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'GitHub Copilot',
        'github-copilot',
        'https://github.com/features/copilot',
        ai_tool_type_id,
        'AI pair programmer that helps you write code faster',
        'GitHub Copilot is an AI coding assistant that provides autocomplete-style suggestions as you code. It is trained on billions of lines of code and can help with various programming languages.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Hugging Face',
        'hugging-face',
        'https://huggingface.co',
        platform_type_id,
        'The AI community building the future of machine learning',
        'Hugging Face is a platform that provides tools and resources for machine learning, including pre-trained models, datasets, and spaces for hosting ML applications.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'OpenAI API',
        'openai-api',
        'https://platform.openai.com',
        api_type_id,
        'Access to OpenAI''s powerful AI models via API',
        'The OpenAI API provides access to powerful AI models including GPT-4, GPT-3.5, DALL-E, and Whisper through a simple REST API interface.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    )
    ON CONFLICT ("name") DO NOTHING;

END $$;

-- Show summary of what was created
SELECT 'Complete database seed with sample entities completed successfully!' as result;
SELECT
    (SELECT COUNT(*) FROM entity_types) as entity_types,
    (SELECT COUNT(*) FROM entities) as entities,
    (SELECT COUNT(*) FROM users) as users,
    (SELECT COUNT(*) FROM app_settings) as app_settings;
