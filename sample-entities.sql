-- =====================================================
-- SAMPLE ENTITIES FOR TESTING
-- Run this AFTER the comprehensive-seed.sql script
-- =====================================================

-- First, we need to create a sample admin user to be the submitter
-- You'll need to replace the auth_user_id with a real one from your Supabase Auth
DO $$ 
DECLARE
    admin_user_id UUID;
    ai_tool_type_id UUID;
    platform_type_id UUID;
    api_type_id UUID;
BEGIN
    -- Create a sample admin user (replace auth_user_id with real one)
    INSERT INTO "public"."users" (
        "id", "auth_user_id", "username", "display_name", "email", 
        "role", "status", "created_at", "updated_at"
    ) VALUES (
        gen_random_uuid(), 
        gen_random_uuid(), -- Replace with real auth user ID when you have one
        'admin', 
        'System Administrator', 
        '<EMAIL>',
        'ADMIN', 
        'ACTIVE', 
        NOW(), 
        NOW()
    ) ON CONFLICT ("auth_user_id") DO NOTHING
    RETURNING id INTO admin_user_id;

    -- If user already exists, get their ID
    IF admin_user_id IS NULL THEN
        SELECT id INTO admin_user_id FROM users WHERE username = 'admin' LIMIT 1;
    END IF;

    -- Get entity type IDs
    SELECT id INTO ai_tool_type_id FROM entity_types WHERE slug = 'ai-tool' LIMIT 1;
    SELECT id INTO platform_type_id FROM entity_types WHERE slug = 'platform' LIMIT 1;
    SELECT id INTO api_type_id FROM entity_types WHERE slug = 'api' LIMIT 1;

    -- Insert sample entities
    INSERT INTO "public"."entities" (
        "id", "name", "slug", "website_url", "entity_type_id", 
        "short_description", "description", "status", "submitter_id",
        "created_at", "updated_at"
    ) VALUES 
    (
        gen_random_uuid(),
        'ChatGPT',
        'chatgpt',
        'https://chat.openai.com',
        ai_tool_type_id,
        'AI-powered conversational assistant by OpenAI',
        'ChatGPT is a large language model developed by OpenAI that can engage in conversational interactions, answer questions, help with writing, coding, analysis, and many other tasks.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Claude',
        'claude',
        'https://claude.ai',
        ai_tool_type_id,
        'AI assistant by Anthropic focused on helpful, harmless, and honest interactions',
        'Claude is an AI assistant created by Anthropic. It is designed to be helpful, harmless, and honest, with strong capabilities in analysis, writing, math, coding, and creative tasks.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Midjourney',
        'midjourney',
        'https://midjourney.com',
        ai_tool_type_id,
        'AI-powered image generation tool',
        'Midjourney is an independent research lab that produces an AI program that creates images from textual descriptions. It is known for its artistic and creative image generation capabilities.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'GitHub Copilot',
        'github-copilot',
        'https://github.com/features/copilot',
        ai_tool_type_id,
        'AI pair programmer that helps you write code faster',
        'GitHub Copilot is an AI coding assistant that provides autocomplete-style suggestions as you code. It is trained on billions of lines of code and can help with various programming languages.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Hugging Face',
        'hugging-face',
        'https://huggingface.co',
        platform_type_id,
        'The AI community building the future of machine learning',
        'Hugging Face is a platform that provides tools and resources for machine learning, including pre-trained models, datasets, and spaces for hosting ML applications.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'OpenAI API',
        'openai-api',
        'https://platform.openai.com',
        api_type_id,
        'Access to OpenAI''s powerful AI models via API',
        'The OpenAI API provides access to powerful AI models including GPT-4, GPT-3.5, DALL-E, and Whisper through a simple REST API interface.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Stable Diffusion',
        'stable-diffusion',
        'https://stability.ai',
        ai_tool_type_id,
        'Open-source AI image generation model',
        'Stable Diffusion is a deep learning, text-to-image model that can generate detailed images from text descriptions. It is open-source and can be run locally.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    ),
    (
        gen_random_uuid(),
        'Notion AI',
        'notion-ai',
        'https://notion.so/ai',
        ai_tool_type_id,
        'AI writing assistant integrated into Notion workspace',
        'Notion AI is an AI-powered writing assistant built into Notion that can help with writing, editing, summarizing, and brainstorming directly within your workspace.',
        'ACTIVE',
        admin_user_id,
        NOW(),
        NOW()
    )
    ON CONFLICT ("name") DO NOTHING;

END $$;

-- Show what was created
SELECT 'Sample entities created successfully!' as result;
SELECT COUNT(*) as entity_count FROM entities;
SELECT COUNT(*) as entity_type_count FROM entity_types;
SELECT COUNT(*) as user_count FROM users;
