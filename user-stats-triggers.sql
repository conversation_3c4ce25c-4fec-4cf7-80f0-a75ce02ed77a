-- =====================================================
-- USER STATISTICS TRIGGERS
-- Run this AFTER the user profile system migration
-- =====================================================

-- Step 1: Create function to update user statistics
CREATE OR REPLACE FUNCTION update_user_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle bookmark statistics
    IF TG_TABLE_NAME = 'user_saved_entities' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE users 
            SET bookmarks_count = bookmarks_count + 1,
                reputation_score = reputation_score + 1
            WHERE id = NEW.user_id;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE users 
            SET bookmarks_count = GREATEST(bookmarks_count - 1, 0),
                reputation_score = GREATEST(reputation_score - 1, 0)
            WHERE id = OLD.user_id;
        END IF;
    END IF;

    -- Handle review statistics
    IF TG_TABLE_NAME = 'reviews' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE users 
            SET reviews_count = reviews_count + 1,
                reputation_score = reputation_score + 3
            WHERE id = NEW.user_id;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE users 
            SET reviews_count = GREATEST(reviews_count - 1, 0),
                reputation_score = GREATEST(reputation_score - 3, 0)
            WHERE id = OLD.user_id;
        END IF;
    END IF;

    -- Handle tool submission statistics
    IF TG_TABLE_NAME = 'user_submitted_tools' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE users 
            SET tools_submitted = tools_submitted + 1,
                reputation_score = reputation_score + 5
            WHERE id = NEW.user_id;
        ELSIF TG_OP = 'UPDATE' THEN
            -- Handle status changes
            IF OLD.submission_status != NEW.submission_status THEN
                IF NEW.submission_status IN ('APPROVED', 'PUBLISHED') AND 
                   OLD.submission_status NOT IN ('APPROVED', 'PUBLISHED') THEN
                    UPDATE users 
                    SET tools_approved = tools_approved + 1,
                        reputation_score = reputation_score + 10
                    WHERE id = NEW.user_id;
                ELSIF OLD.submission_status IN ('APPROVED', 'PUBLISHED') AND 
                      NEW.submission_status NOT IN ('APPROVED', 'PUBLISHED') THEN
                    UPDATE users 
                    SET tools_approved = GREATEST(tools_approved - 1, 0),
                        reputation_score = GREATEST(reputation_score - 10, 0)
                    WHERE id = NEW.user_id;
                END IF;
            END IF;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE users 
            SET tools_submitted = GREATEST(tools_submitted - 1, 0),
                reputation_score = GREATEST(reputation_score - 5, 0)
            WHERE id = OLD.user_id;
            
            -- Also remove approval points if it was approved
            IF OLD.submission_status IN ('APPROVED', 'PUBLISHED') THEN
                UPDATE users 
                SET tools_approved = GREATEST(tools_approved - 1, 0),
                    reputation_score = GREATEST(reputation_score - 10, 0)
                WHERE id = OLD.user_id;
            END IF;
        END IF;
    END IF;

    -- Handle tool request statistics
    IF TG_TABLE_NAME = 'tool_requests' THEN
        IF TG_OP = 'INSERT' THEN
            UPDATE users 
            SET requests_made = requests_made + 1
            WHERE id = NEW.user_id;
        ELSIF TG_OP = 'UPDATE' THEN
            -- Handle status changes
            IF OLD.status != NEW.status AND NEW.status = 'COMPLETED' THEN
                UPDATE users 
                SET requests_fulfilled = requests_fulfilled + 1,
                    reputation_score = reputation_score + 2
                WHERE id = NEW.user_id;
            ELSIF OLD.status = 'COMPLETED' AND NEW.status != 'COMPLETED' THEN
                UPDATE users 
                SET requests_fulfilled = GREATEST(requests_fulfilled - 1, 0),
                    reputation_score = GREATEST(reputation_score - 2, 0)
                WHERE id = NEW.user_id;
            END IF;
        ELSIF TG_OP = 'DELETE' THEN
            UPDATE users 
            SET requests_made = GREATEST(requests_made - 1, 0)
            WHERE id = OLD.user_id;
            
            -- Also remove completion points if it was completed
            IF OLD.status = 'COMPLETED' THEN
                UPDATE users 
                SET requests_fulfilled = GREATEST(requests_fulfilled - 1, 0),
                    reputation_score = GREATEST(reputation_score - 2, 0)
                WHERE id = OLD.user_id;
            END IF;
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Step 2: Create triggers for user_saved_entities (if table exists)
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_saved_entities') THEN
        DROP TRIGGER IF EXISTS trigger_update_user_bookmarks ON user_saved_entities;
        CREATE TRIGGER trigger_update_user_bookmarks
            AFTER INSERT OR DELETE ON user_saved_entities
            FOR EACH ROW EXECUTE FUNCTION update_user_statistics();
    END IF;
END $$;

-- Step 3: Create triggers for reviews (if table exists)
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reviews') THEN
        DROP TRIGGER IF EXISTS trigger_update_user_reviews ON reviews;
        CREATE TRIGGER trigger_update_user_reviews
            AFTER INSERT OR DELETE ON reviews
            FOR EACH ROW EXECUTE FUNCTION update_user_statistics();
    END IF;
END $$;

-- Step 4: Create triggers for user_submitted_tools
DROP TRIGGER IF EXISTS trigger_update_user_submissions ON user_submitted_tools;
CREATE TRIGGER trigger_update_user_submissions
    AFTER INSERT OR UPDATE OR DELETE ON user_submitted_tools
    FOR EACH ROW EXECUTE FUNCTION update_user_statistics();

-- Step 5: Create triggers for tool_requests
DROP TRIGGER IF EXISTS trigger_update_user_requests ON tool_requests;
CREATE TRIGGER trigger_update_user_requests
    AFTER INSERT OR UPDATE OR DELETE ON tool_requests
    FOR EACH ROW EXECUTE FUNCTION update_user_statistics();

-- Step 6: Create function to clean up old profile activities
CREATE OR REPLACE FUNCTION cleanup_old_profile_activities()
RETURNS TRIGGER AS $$
BEGIN
    -- Keep only the latest 50 activities per user
    DELETE FROM profile_activities 
    WHERE user_id = NEW.user_id 
    AND id NOT IN (
        SELECT id FROM profile_activities 
        WHERE user_id = NEW.user_id 
        ORDER BY created_at DESC 
        LIMIT 50
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create trigger for profile activities cleanup
DROP TRIGGER IF EXISTS trigger_cleanup_profile_activities ON profile_activities;
CREATE TRIGGER trigger_cleanup_profile_activities
    AFTER INSERT ON profile_activities
    FOR EACH ROW EXECUTE FUNCTION cleanup_old_profile_activities();

-- Step 8: Update existing user statistics (recalculate from scratch)
UPDATE users SET 
    bookmarks_count = COALESCE((
        SELECT COUNT(*) FROM user_saved_entities 
        WHERE user_id = users.id
    ), 0),
    reviews_count = COALESCE((
        SELECT COUNT(*) FROM reviews 
        WHERE user_id = users.id
    ), 0),
    tools_submitted = COALESCE((
        SELECT COUNT(*) FROM user_submitted_tools 
        WHERE user_id = users.id
    ), 0),
    tools_approved = COALESCE((
        SELECT COUNT(*) FROM user_submitted_tools 
        WHERE user_id = users.id 
        AND submission_status IN ('APPROVED', 'PUBLISHED')
    ), 0),
    requests_made = COALESCE((
        SELECT COUNT(*) FROM tool_requests 
        WHERE user_id = users.id
    ), 0),
    requests_fulfilled = COALESCE((
        SELECT COUNT(*) FROM tool_requests 
        WHERE user_id = users.id 
        AND status = 'COMPLETED'
    ), 0)
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users');

-- Step 9: Recalculate reputation scores
UPDATE users SET 
    reputation_score = (
        (tools_submitted * 5) + 
        (tools_approved * 10) + 
        (reviews_count * 3) + 
        (requests_fulfilled * 2) + 
        bookmarks_count
    )
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users');

-- Success message
SELECT 'User statistics triggers created successfully!' as result;
SELECT 'Database recovery completed!' as final_status;
